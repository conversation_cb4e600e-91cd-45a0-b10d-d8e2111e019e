"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AppLayout */ \"(app-pages-browser)/./src/components/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* harmony import */ var _lib_user_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/user-utils */ \"(app-pages-browser)/./src/lib/user-utils.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _session_user, _profile_firstName, _profile_lastName;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Theme state\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [compactMode, setCompactMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [highContrast, setHighContrast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Security form state\n    const [currentPassword, setCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Departments state\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentsLoading, setDepartmentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Profile form state\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        jobTitle: '',\n        department: '',\n        departmentId: '',\n        phone: '',\n        bio: ''\n    });\n    // Load user preferences from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (true) {\n                // Load theme settings\n                const savedTheme = localStorage.getItem('app-theme') || 'light';\n                setTheme(savedTheme);\n                document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n                // Load other appearance settings\n                setCompactMode(localStorage.getItem('compact-mode') === 'true');\n                setHighContrast(localStorage.getItem('high-contrast') === 'true');\n                // Apply compact mode if enabled\n                document.documentElement.classList.toggle('compact', compactMode);\n                // Apply high contrast if enabled\n                document.documentElement.classList.toggle('high-contrast', highContrast);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Fetch departments\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const fetchDepartments = {\n                \"SettingsPage.useEffect.fetchDepartments\": async ()=>{\n                    try {\n                        setDepartmentsLoading(true);\n                        const response = await fetch('/api/departments');\n                        if (!response.ok) {\n                            throw new Error(\"Error \".concat(response.status, \": \").concat(response.statusText));\n                        }\n                        const data = await response.json();\n                        setDepartments(data);\n                    } catch (error) {\n                        console.error('Failed to fetch departments:', error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to load departments. Using default values instead.');\n                    } finally{\n                        setDepartmentsLoading(false);\n                    }\n                }\n            }[\"SettingsPage.useEffect.fetchDepartments\"];\n            fetchDepartments();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Load user data from API (not session)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const loadUserProfile = {\n                \"SettingsPage.useEffect.loadUserProfile\": async ()=>{\n                    var _session_user;\n                    if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                        try {\n                            const response = await fetch('/api/user/profile');\n                            if (response.ok) {\n                                const userData = await response.json();\n                                // Parse the full name into first and last name\n                                const fullName = userData.name || '';\n                                const nameParts = fullName.split(' ');\n                                const firstName = nameParts[0] || '';\n                                const lastName = nameParts.slice(1).join(' ') || '';\n                                setProfile({\n                                    firstName,\n                                    lastName,\n                                    email: userData.email || '',\n                                    jobTitle: userData.jobTitle || '',\n                                    department: userData.department || '',\n                                    departmentId: userData.departmentId || '',\n                                    phone: userData.phone || '',\n                                    bio: userData.bio || ''\n                                });\n                            } else {\n                                // Fallback to session data if API fails\n                                const fullName = session.user.name || '';\n                                const nameParts = fullName.split(' ');\n                                const firstName = nameParts[0] || '';\n                                const lastName = nameParts.slice(1).join(' ') || '';\n                                setProfile({\n                                    firstName,\n                                    lastName,\n                                    email: session.user.email || '',\n                                    jobTitle: '',\n                                    department: '',\n                                    departmentId: '',\n                                    phone: '',\n                                    bio: ''\n                                });\n                            }\n                        } catch (error) {\n                            console.error('Error loading user profile:', error);\n                            // Fallback to session data\n                            const fullName = session.user.name || '';\n                            const nameParts = fullName.split(' ');\n                            const firstName = nameParts[0] || '';\n                            const lastName = nameParts.slice(1).join(' ') || '';\n                            setProfile({\n                                firstName,\n                                lastName,\n                                email: session.user.email || '',\n                                jobTitle: '',\n                                department: '',\n                                departmentId: '',\n                                phone: '',\n                                bio: ''\n                            });\n                        }\n                    }\n                }\n            }[\"SettingsPage.useEffect.loadUserProfile\"];\n            loadUserProfile();\n        }\n    }[\"SettingsPage.useEffect\"], [\n        session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id\n    ]); // Only depend on user ID, not the entire session\n    // Handle theme change\n    const handleThemeChange = (newTheme)=>{\n        setTheme(newTheme);\n        localStorage.setItem('app-theme', newTheme);\n        // Apply theme to document\n        if (newTheme === 'dark') {\n            document.documentElement.classList.add('dark');\n        } else if (newTheme === 'light') {\n            document.documentElement.classList.remove('dark');\n        } else if (newTheme === 'auto') {\n            // Check system preference\n            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n            document.documentElement.classList.toggle('dark', prefersDark);\n        }\n    };\n    // Handle compact mode toggle\n    const handleCompactModeToggle = ()=>{\n        const newValue = !compactMode;\n        setCompactMode(newValue);\n        localStorage.setItem('compact-mode', String(newValue));\n        document.documentElement.classList.toggle('compact', newValue);\n    };\n    // Handle high contrast toggle\n    const handleHighContrastToggle = ()=>{\n        const newValue = !highContrast;\n        setHighContrast(newValue);\n        localStorage.setItem('high-contrast', String(newValue));\n        document.documentElement.classList.toggle('high-contrast', newValue);\n    };\n    // Handle password update\n    const handlePasswordUpdate = async (e)=>{\n        e.preventDefault();\n        setPasswordError('');\n        // Validation\n        if (newPassword !== confirmPassword) {\n            setPasswordError('New passwords do not match');\n            return;\n        }\n        if (newPassword.length < 6) {\n            setPasswordError('Password must be at least 6 characters');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Update password via API\n            const response = await fetch('/api/user/password', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    currentPassword,\n                    newPassword\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to update password');\n            }\n            // Clear form\n            setCurrentPassword('');\n            setNewPassword('');\n            setConfirmPassword('');\n            setPasswordError('');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Password updated successfully');\n        } catch (error) {\n            console.error('Error updating password:', error);\n            if (error instanceof Error) {\n                setPasswordError(error.message);\n            } else {\n                setPasswordError('An unexpected error occurred');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle profile update\n    const handleProfileUpdate = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            var _session_user;\n            // Validate form data\n            if (!profile.firstName.trim() || !profile.lastName.trim()) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('First name and last name are required');\n                return;\n            }\n            if (!profile.email.trim() || !profile.email.includes('@')) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Please enter a valid email address');\n                return;\n            }\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                // Update user profile via API\n                const response = await fetch('/api/user/profile', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: \"\".concat(profile.firstName.trim(), \" \").concat(profile.lastName.trim()),\n                        email: profile.email.trim(),\n                        department: profile.department,\n                        departmentId: profile.departmentId,\n                        phone: profile.phone,\n                        bio: profile.bio,\n                        jobTitle: profile.jobTitle\n                    })\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to update profile');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Profile updated successfully');\n                // Trigger session update to reflect name changes in UI\n                if (true) {\n                    window.location.reload();\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('No user session found');\n            }\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to update profile');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Request password reset\n    const handlePasswordReset = async ()=>{\n        try {\n            var _session_user;\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email) {\n                await (0,_lib_user_utils__WEBPACK_IMPORTED_MODULE_5__.sendPasswordReset)(session.user.email);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Password reset email sent');\n            }\n        } catch (error) {\n            console.error('Error sending password reset:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to send password reset email');\n        }\n    };\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 46\n            }, this)\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            id: 'appearance',\n            label: 'Appearance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 337,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            id: 'integrations',\n            label: 'Integrations',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 56\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Settings\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-500 dark:text-slate-400 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 dark:bg-slate-800 p-6 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-1\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"w-full flex items-center text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors \".concat(activeTab === tab.id ? 'bg-blue-500 text-white' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3\",\n                                                    children: tab.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-3 p-6\",\n                                children: [\n                                    activeTab === 'profile' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Profile Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                className: \"space-y-6\",\n                                                onSubmit: handleProfileUpdate,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl font-medium mr-6\",\n                                                                children: [\n                                                                    ((_profile_firstName = profile.firstName) === null || _profile_firstName === void 0 ? void 0 : _profile_firstName[0]) || 'U',\n                                                                    ((_profile_lastName = profile.lastName) === null || _profile_lastName === void 0 ? void 0 : _profile_lastName[0]) || ''\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-base font-medium text-slate-900 dark:text-white mb-1\",\n                                                                        children: \"Profile Picture\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement file upload\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('Profile picture upload coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Upload new\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-slate-500 dark:text-slate-400 hover:text-slate-600 dark:hover:text-slate-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement remove picture\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('Remove picture feature coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Remove\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"First Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 38\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.firstName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.firstName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                firstName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your first name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.firstName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"First name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Last Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.lastName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.lastName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                lastName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your last name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.lastName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Last name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Email \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white bg-slate-50 dark:bg-slate-900\",\n                                                                        value: profile.email,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                email: e.target.value\n                                                                            }),\n                                                                        disabled: true,\n                                                                        title: \"Email cannot be changed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Email cannot be changed. Contact administrator if needed.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Job Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.jobTitle,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                jobTitle: e.target.value\n                                                                            }),\n                                                                        placeholder: \"e.g., Project Manager, Developer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.departmentId || '',\n                                                                        onChange: (e)=>{\n                                                                            const deptId = e.target.value;\n                                                                            const selectedDept = departments.find((d)=>d.id === deptId);\n                                                                            setProfile({\n                                                                                ...profile,\n                                                                                departmentId: deptId,\n                                                                                department: (selectedDept === null || selectedDept === void 0 ? void 0 : selectedDept.name) || ''\n                                                                            });\n                                                                        },\n                                                                        disabled: departmentsLoading,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"Select Department\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 492,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: dept.id,\n                                                                                    children: dept.name\n                                                                                }, dept.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 494,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    departmentsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Loading departments...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"tel\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.phone,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                phone: e.target.value\n                                                                            }),\n                                                                        placeholder: \"+968 9123 4567\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"md:col-span-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Bio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        className: \"input min-h-[100px] dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        rows: 4,\n                                                                        value: profile.bio,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                bio: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Tell us about yourself, your experience, and expertise...\",\n                                                                        maxLength: 500\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: [\n                                                                            profile.bio.length,\n                                                                            \"/500 characters\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center pt-4 border-t border-slate-200 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Required fields\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"btn btn-secondary\",\n                                                                        onClick: ()=>{\n                                                                            // Reset form to original values\n                                                                            if (session === null || session === void 0 ? void 0 : session.user) {\n                                                                                const fullName = session.user.name || '';\n                                                                                const nameParts = fullName.split(' ');\n                                                                                const firstName = nameParts[0] || '';\n                                                                                const lastName = nameParts.slice(1).join(' ') || '';\n                                                                                setProfile({\n                                                                                    firstName,\n                                                                                    lastName,\n                                                                                    email: session.user.email || '',\n                                                                                    jobTitle: 'Project Manager',\n                                                                                    department: '',\n                                                                                    departmentId: '',\n                                                                                    phone: '',\n                                                                                    bio: \"\".concat(session.user.role || 'Team member', \" with expertise in project management.\")\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: \"Reset\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        className: \"btn btn-primary\",\n                                                                        disabled: isLoading || !profile.firstName.trim() || !profile.lastName.trim(),\n                                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                            className: \"opacity-25\",\n                                                                                            cx: \"12\",\n                                                                                            cy: \"12\",\n                                                                                            r: \"10\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            strokeWidth: \"4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 574,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            className: \"opacity-75\",\n                                                                                            fill: \"currentColor\",\n                                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 575,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Saving...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 29\n                                                                        }, this) : 'Save Changes'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    {\n                                                        title: 'Email Notifications',\n                                                        desc: 'Receive notifications via email'\n                                                    },\n                                                    {\n                                                        title: 'Project Updates',\n                                                        desc: 'Get notified when projects are updated'\n                                                    },\n                                                    {\n                                                        title: 'Task Assignments',\n                                                        desc: 'Notifications for new task assignments'\n                                                    },\n                                                    {\n                                                        title: 'Deadline Reminders',\n                                                        desc: 'Reminders for upcoming deadlines'\n                                                    },\n                                                    {\n                                                        title: 'Team Mentions',\n                                                        desc: 'When someone mentions you in comments'\n                                                    }\n                                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-4 border-b border-slate-200 dark:border-slate-700 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                        children: item.desc\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"sr-only peer\",\n                                                                        defaultChecked: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'appearance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Appearance Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    {\n                                                                        name: 'Light',\n                                                                        desc: 'Clean and bright interface',\n                                                                        value: 'light'\n                                                                    },\n                                                                    {\n                                                                        name: 'Dark',\n                                                                        desc: 'Easy on the eyes',\n                                                                        value: 'dark'\n                                                                    },\n                                                                    {\n                                                                        name: 'Auto',\n                                                                        desc: 'Matches system preference',\n                                                                        value: 'auto'\n                                                                    }\n                                                                ].map((themeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card p-4 cursor-pointer border-2 \".concat(theme === themeOption.value ? 'border-blue-500' : 'border-transparent', \" dark:bg-slate-800\"),\n                                                                        onClick: ()=>handleThemeChange(themeOption.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                children: themeOption.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                children: themeOption.desc\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, themeOption.value, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Display Options\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"Compact Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 644,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Reduce spacing for more content\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 645,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: compactMode,\n                                                                                        onChange: handleCompactModeToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 648,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 654,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 642,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"High Contrast\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 660,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Increase contrast for better visibility\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 661,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 659,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: highContrast,\n                                                                                        onChange: handleHighContrastToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 664,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 670,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 663,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Change Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                                onSubmit: handlePasswordUpdate,\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Current Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: currentPassword,\n                                                                                onChange: (e)=>setCurrentPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: newPassword,\n                                                                                onChange: (e)=>setNewPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Confirm New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 707,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: confirmPassword,\n                                                                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-red-500 text-sm\",\n                                                                        children: passwordError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"submit\",\n                                                                                className: \"btn btn-primary\",\n                                                                                disabled: isLoading,\n                                                                                children: isLoading ? 'Updating...' : 'Update Password'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 722,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"btn btn-secondary\",\n                                                                                onClick: handlePasswordReset,\n                                                                                children: \"Send Reset Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 729,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Login Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-600 dark:text-slate-400 mb-4\",\n                                                                children: \"Monitor your account login activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-slate-50 dark:bg-slate-700 p-3 rounded-lg mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-900 dark:text-white font-medium\",\n                                                                        children: \"Current Session\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            \"Active now - \",\n                                                                            new Date().toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"btn btn-secondary text-sm\",\n                                                                children: \"View All Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'integrations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    {\n                                                        name: 'Slack',\n                                                        desc: 'Connect with your Slack workspace',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Microsoft Teams',\n                                                        desc: 'Integrate with Teams for notifications',\n                                                        connected: false\n                                                    },\n                                                    {\n                                                        name: 'Google Calendar',\n                                                        desc: 'Sync project deadlines with calendar',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Jira',\n                                                        desc: 'Import and sync Jira issues',\n                                                        connected: false\n                                                    }\n                                                ].map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                                            children: integration.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                            children: integration.desc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 767,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn \".concat(integration.connected ? 'btn-secondary' : 'btn-primary'),\n                                                                    children: integration.connected ? 'Disconnect' : 'Connect'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 344,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 343,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"IysQmi3MYF+YSWwMev/AVTT0jvw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});