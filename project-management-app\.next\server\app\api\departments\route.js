/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/departments/route";
exports.ids = ["app/api/departments/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdepartments%2Froute&page=%2Fapi%2Fdepartments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdepartments%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdepartments%2Froute&page=%2Fapi%2Fdepartments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdepartments%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_departments_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/departments/route.ts */ \"(rsc)/./src/app/api/departments/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/departments/route\",\n        pathname: \"/api/departments\",\n        filename: \"route\",\n        bundlePath: \"app/api/departments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\api\\\\departments\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_departments_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdepartments%2Froute&page=%2Fapi%2Fdepartments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdepartments%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/departments/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/departments/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_departmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/departmentService */ \"(rsc)/./src/services/departmentService.ts\");\n\n\n// GET /api/departments - Get all departments\nasync function GET() {\n    try {\n        const departments = await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.getAllDepartments)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(departments);\n    } catch (error) {\n        console.error('Error fetching departments:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch departments'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/departments - Create a new department\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        if (!body.name || body.name.trim() === '') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Department name is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if department with same name already exists\n        const exists = await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.departmentNameExists)(body.name);\n        if (exists) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Department with this name already exists'\n            }, {\n                status: 400\n            });\n        }\n        // Create the department\n        const department = await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.createDepartment)({\n            name: body.name,\n            description: body.description || null,\n            budget: body.budget ? parseFloat(body.budget) : null\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(department, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating department:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create department'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/departments - Update a department\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        if (!body.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Department ID is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!body.name || body.name.trim() === '') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Department name is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if another department with the same name exists\n        const exists = await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.departmentNameExists)(body.name, body.id);\n        if (exists) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Another department with this name already exists'\n            }, {\n                status: 400\n            });\n        }\n        const department = await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.updateDepartment)(body.id, {\n            name: body.name,\n            description: body.description || null,\n            budget: body.budget ? parseFloat(body.budget) : null\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(department);\n    } catch (error) {\n        console.error('Error updating department:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update department'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/departments?id={id} - Delete a department\nasync function DELETE(request) {\n    try {\n        const id = request.nextUrl.searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Department ID is required'\n            }, {\n                status: 400\n            });\n        }\n        await (0,_services_departmentService__WEBPACK_IMPORTED_MODULE_1__.deleteDepartment)(id);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n            status: 204\n        });\n    } catch (error) {\n        console.error('Error deleting department:', error);\n        // Check for specific error messages\n        if (error.message?.includes('associated users') || error.message?.includes('associated projects')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete department'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/departments/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n// Import the functions you need from the SDKs you need\n\n\n\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k\",\n    authDomain: \"project-management-f45cc.firebaseapp.com\",\n    projectId: \"project-management-f45cc\",\n    storageBucket: \"project-management-f45cc.firebasestorage.app\",\n    messagingSenderId: \"1002222709659\",\n    appId: \"1:1002222709659:web:6b1ab479efcc4102824f3e\",\n    measurementId: \"G-JYYNYZV8LP\"\n};\n// Initialize Firebase only if we don't already have an instance\n// This helps prevent multiple initializations during SSR/SSG\nlet app;\nif (!(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Analytics is now null by default\nconst analytics = null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/departmentService.ts":
/*!*******************************************!*\
  !*** ./src/services/departmentService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDepartment: () => (/* binding */ createDepartment),\n/* harmony export */   deleteDepartment: () => (/* binding */ deleteDepartment),\n/* harmony export */   departmentNameExists: () => (/* binding */ departmentNameExists),\n/* harmony export */   getAllDepartments: () => (/* binding */ getAllDepartments),\n/* harmony export */   getDepartmentById: () => (/* binding */ getDepartmentById),\n/* harmony export */   updateDepartment: () => (/* binding */ updateDepartment)\n/* harmony export */ });\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Convert Firestore document to Department object\nconst departmentConverter = {\n    fromFirestore (snapshot) {\n        const data = snapshot.data();\n        // Handle Firestore timestamps\n        let createdAt = new Date();\n        let updatedAt = new Date();\n        if (data.createdAt) {\n            if (typeof data.createdAt.toDate === 'function') {\n                createdAt = data.createdAt.toDate();\n            } else if (data.createdAt.seconds) {\n                createdAt = new Date(data.createdAt.seconds * 1000);\n            }\n        }\n        if (data.updatedAt) {\n            if (typeof data.updatedAt.toDate === 'function') {\n                updatedAt = data.updatedAt.toDate();\n            } else if (data.updatedAt.seconds) {\n                updatedAt = new Date(data.updatedAt.seconds * 1000);\n            }\n        }\n        return {\n            id: snapshot.id,\n            name: data.name,\n            description: data.description || null,\n            budget: data.budget || null,\n            createdAt: createdAt,\n            updatedAt: updatedAt\n        };\n    },\n    toFirestore (department) {\n        return {\n            name: department.name,\n            description: department.description || null,\n            budget: department.budget || null,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n    }\n};\n// Collection reference\nconst departmentsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments');\n// Get all departments with stats\nasync function getAllDepartments() {\n    try {\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(departmentsCollection);\n        // Get all departments\n        const departments = snapshot.docs.map((doc)=>departmentConverter.fromFirestore(doc));\n        // For each department, get related counts\n        const departmentsWithStats = await Promise.all(departments.map(async (dept)=>{\n            // Get users in department\n            const usersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', dept.id));\n            const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(usersQuery);\n            // Get projects in department\n            const projectsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', dept.id));\n            const projectsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(projectsQuery);\n            // Calculate total budget from projects\n            let totalBudget = 0;\n            projectsSnapshot.docs.forEach((doc)=>{\n                const projectData = doc.data();\n                totalBudget += projectData.budget || 0;\n            });\n            // Return department with stats\n            return {\n                ...dept,\n                memberCount: usersSnapshot.size,\n                projectCount: projectsSnapshot.size,\n                totalBudget\n            };\n        }));\n        return departmentsWithStats;\n    } catch (error) {\n        console.error('Error getting departments:', error);\n        throw error;\n    }\n}\n// Get department by ID\nasync function getDepartmentById(id) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments', id);\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!snapshot.exists()) {\n            return null;\n        }\n        return departmentConverter.fromFirestore(snapshot);\n    } catch (error) {\n        console.error(`Error getting department ${id}:`, error);\n        throw error;\n    }\n}\n// Create new department\nasync function createDepartment(departmentData) {\n    try {\n        const data = {\n            ...departmentData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(departmentsCollection, data);\n        const newDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        return departmentConverter.fromFirestore(newDoc);\n    } catch (error) {\n        console.error('Error creating department:', error);\n        throw error;\n    }\n}\n// Update department\nasync function updateDepartment(id, departmentData) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments', id);\n        // Add updatedAt timestamp\n        const data = {\n            ...departmentData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(docRef, data);\n        // Get updated document\n        const updatedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!updatedDoc.exists()) {\n            throw new Error(`Department with ID ${id} not found`);\n        }\n        return departmentConverter.fromFirestore(updatedDoc);\n    } catch (error) {\n        console.error(`Error updating department ${id}:`, error);\n        throw error;\n    }\n}\n// Delete department\nasync function deleteDepartment(id) {\n    try {\n        // Check if department has any users or projects\n        const usersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', id));\n        const usersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(usersQuery);\n        if (!usersSnapshot.empty) {\n            throw new Error('Cannot delete department with associated users');\n        }\n        const projectsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects'), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', id));\n        const projectsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(projectsQuery);\n        if (!projectsSnapshot.empty) {\n            throw new Error('Cannot delete department with associated projects');\n        }\n        // Delete the department\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments', id));\n    } catch (error) {\n        console.error(`Error deleting department ${id}:`, error);\n        throw error;\n    }\n}\n// Check if department name exists\nasync function departmentNameExists(name, excludeId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(departmentsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('name', '==', name));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (snapshot.empty) {\n            return false;\n        }\n        // If we're checking for an update operation, exclude the current department\n        if (excludeId) {\n            return snapshot.docs.some((doc)=>doc.id !== excludeId);\n        }\n        return true;\n    } catch (error) {\n        console.error(`Error checking if department name ${name} exists:`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/departmentService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdepartments%2Froute&page=%2Fapi%2Fdepartments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdepartments%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();