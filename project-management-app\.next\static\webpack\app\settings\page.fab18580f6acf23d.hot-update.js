"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AppLayout */ \"(app-pages-browser)/./src/components/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* harmony import */ var _lib_user_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/user-utils */ \"(app-pages-browser)/./src/lib/user-utils.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _profile_firstName, _profile_lastName;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Theme state\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [compactMode, setCompactMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [highContrast, setHighContrast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Security form state\n    const [currentPassword, setCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Departments state\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentsLoading, setDepartmentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Profile form state\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        jobTitle: '',\n        department: '',\n        departmentId: '',\n        phone: '',\n        bio: ''\n    });\n    // Load user preferences from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (true) {\n                // Load theme settings\n                const savedTheme = localStorage.getItem('app-theme') || 'light';\n                setTheme(savedTheme);\n                document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n                // Load other appearance settings\n                setCompactMode(localStorage.getItem('compact-mode') === 'true');\n                setHighContrast(localStorage.getItem('high-contrast') === 'true');\n                // Apply compact mode if enabled\n                document.documentElement.classList.toggle('compact', compactMode);\n                // Apply high contrast if enabled\n                document.documentElement.classList.toggle('high-contrast', highContrast);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Fetch departments\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const fetchDepartments = {\n                \"SettingsPage.useEffect.fetchDepartments\": async ()=>{\n                    try {\n                        setDepartmentsLoading(true);\n                        const response = await fetch('/api/departments');\n                        if (!response.ok) {\n                            throw new Error(\"Error \".concat(response.status, \": \").concat(response.statusText));\n                        }\n                        const data = await response.json();\n                        setDepartments(data);\n                    } catch (error) {\n                        console.error('Failed to fetch departments:', error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to load departments. Using default values instead.');\n                    } finally{\n                        setDepartmentsLoading(false);\n                    }\n                }\n            }[\"SettingsPage.useEffect.fetchDepartments\"];\n            fetchDepartments();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Load user data from session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                const fullName = session.user.name || '';\n                const nameParts = fullName.split(' ');\n                const firstName = nameParts[0] || '';\n                const lastName = nameParts.slice(1).join(' ') || '';\n                setProfile({\n                    \"SettingsPage.useEffect\": (prev)=>({\n                            ...prev,\n                            firstName,\n                            lastName,\n                            email: session.user.email || '',\n                            // Keep existing values for fields not in session or set defaults\n                            jobTitle: prev.jobTitle || 'Project Manager',\n                            department: prev.department || '',\n                            departmentId: prev.departmentId || '',\n                            phone: prev.phone || '',\n                            bio: prev.bio || \"\".concat(session.user.role || 'Team member', \" with expertise in project management.\")\n                        })\n                }[\"SettingsPage.useEffect\"]);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        session\n    ]);\n    // Handle theme change\n    const handleThemeChange = (newTheme)=>{\n        setTheme(newTheme);\n        localStorage.setItem('app-theme', newTheme);\n        // Apply theme to document\n        if (newTheme === 'dark') {\n            document.documentElement.classList.add('dark');\n        } else if (newTheme === 'light') {\n            document.documentElement.classList.remove('dark');\n        } else if (newTheme === 'auto') {\n            // Check system preference\n            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n            document.documentElement.classList.toggle('dark', prefersDark);\n        }\n    };\n    // Handle compact mode toggle\n    const handleCompactModeToggle = ()=>{\n        const newValue = !compactMode;\n        setCompactMode(newValue);\n        localStorage.setItem('compact-mode', String(newValue));\n        document.documentElement.classList.toggle('compact', newValue);\n    };\n    // Handle high contrast toggle\n    const handleHighContrastToggle = ()=>{\n        const newValue = !highContrast;\n        setHighContrast(newValue);\n        localStorage.setItem('high-contrast', String(newValue));\n        document.documentElement.classList.toggle('high-contrast', newValue);\n    };\n    // Handle password update\n    const handlePasswordUpdate = async (e)=>{\n        e.preventDefault();\n        setPasswordError('');\n        // Validation\n        if (newPassword !== confirmPassword) {\n            setPasswordError('New passwords do not match');\n            return;\n        }\n        if (newPassword.length < 6) {\n            setPasswordError('Password must be at least 6 characters');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Update password via API\n            const response = await fetch('/api/user/password', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    currentPassword,\n                    newPassword\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to update password');\n            }\n            // Clear form\n            setCurrentPassword('');\n            setNewPassword('');\n            setConfirmPassword('');\n            setPasswordError('');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Password updated successfully');\n        } catch (error) {\n            console.error('Error updating password:', error);\n            if (error instanceof Error) {\n                setPasswordError(error.message);\n            } else {\n                setPasswordError('An unexpected error occurred');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle profile update\n    const handleProfileUpdate = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            var _session_user;\n            // Validate form data\n            if (!profile.firstName.trim() || !profile.lastName.trim()) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('First name and last name are required');\n                return;\n            }\n            if (!profile.email.trim() || !profile.email.includes('@')) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Please enter a valid email address');\n                return;\n            }\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                // Update user profile via API\n                const response = await fetch('/api/user/profile', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: \"\".concat(profile.firstName.trim(), \" \").concat(profile.lastName.trim()),\n                        email: profile.email.trim(),\n                        department: profile.department,\n                        departmentId: profile.departmentId,\n                        phone: profile.phone,\n                        bio: profile.bio,\n                        jobTitle: profile.jobTitle\n                    })\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to update profile');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Profile updated successfully');\n                // Trigger session update to reflect name changes in UI\n                if (true) {\n                    window.location.reload();\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('No user session found');\n            }\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to update profile');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Request password reset\n    const handlePasswordReset = async ()=>{\n        try {\n            var _session_user;\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email) {\n                await (0,_lib_user_utils__WEBPACK_IMPORTED_MODULE_5__.sendPasswordReset)(session.user.email);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Password reset email sent');\n            }\n        } catch (error) {\n            console.error('Error sending password reset:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Failed to send password reset email');\n        }\n    };\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 290,\n                columnNumber: 46\n            }, this)\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            id: 'appearance',\n            label: 'Appearance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            id: 'integrations',\n            label: 'Integrations',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 56\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Settings\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-500 dark:text-slate-400 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 dark:bg-slate-800 p-6 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-1\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"w-full flex items-center text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors \".concat(activeTab === tab.id ? 'bg-blue-500 text-white' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3\",\n                                                    children: tab.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-3 p-6\",\n                                children: [\n                                    activeTab === 'profile' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Profile Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                className: \"space-y-6\",\n                                                onSubmit: handleProfileUpdate,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl font-medium mr-6\",\n                                                                children: [\n                                                                    ((_profile_firstName = profile.firstName) === null || _profile_firstName === void 0 ? void 0 : _profile_firstName[0]) || 'U',\n                                                                    ((_profile_lastName = profile.lastName) === null || _profile_lastName === void 0 ? void 0 : _profile_lastName[0]) || ''\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-base font-medium text-slate-900 dark:text-white mb-1\",\n                                                                        children: \"Profile Picture\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement file upload\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('Profile picture upload coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Upload new\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-slate-500 dark:text-slate-400 hover:text-slate-600 dark:hover:text-slate-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement remove picture\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('Remove picture feature coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Remove\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"First Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 38\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.firstName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.firstName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                firstName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your first name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.firstName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"First name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Last Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.lastName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.lastName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                lastName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your last name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.lastName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Last name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Email \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white bg-slate-50 dark:bg-slate-900\",\n                                                                        value: profile.email,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                email: e.target.value\n                                                                            }),\n                                                                        disabled: true,\n                                                                        title: \"Email cannot be changed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Email cannot be changed. Contact administrator if needed.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Job Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.jobTitle,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                jobTitle: e.target.value\n                                                                            }),\n                                                                        placeholder: \"e.g., Project Manager, Developer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.departmentId || '',\n                                                                        onChange: (e)=>{\n                                                                            const deptId = e.target.value;\n                                                                            const selectedDept = departments.find((d)=>d.id === deptId);\n                                                                            setProfile({\n                                                                                ...profile,\n                                                                                departmentId: deptId,\n                                                                                department: (selectedDept === null || selectedDept === void 0 ? void 0 : selectedDept.name) || ''\n                                                                            });\n                                                                        },\n                                                                        disabled: departmentsLoading,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"Select Department\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: dept.id,\n                                                                                    children: dept.name\n                                                                                }, dept.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    departmentsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Loading departments...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"tel\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.phone,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                phone: e.target.value\n                                                                            }),\n                                                                        placeholder: \"+968 9123 4567\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"md:col-span-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Bio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        className: \"input min-h-[100px] dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        rows: 4,\n                                                                        value: profile.bio,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                bio: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Tell us about yourself, your experience, and expertise...\",\n                                                                        maxLength: 500\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: [\n                                                                            profile.bio.length,\n                                                                            \"/500 characters\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center pt-4 border-t border-slate-200 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Required fields\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"btn btn-secondary\",\n                                                                        onClick: ()=>{\n                                                                            // Reset form to original values\n                                                                            if (session === null || session === void 0 ? void 0 : session.user) {\n                                                                                const fullName = session.user.name || '';\n                                                                                const nameParts = fullName.split(' ');\n                                                                                const firstName = nameParts[0] || '';\n                                                                                const lastName = nameParts.slice(1).join(' ') || '';\n                                                                                setProfile({\n                                                                                    firstName,\n                                                                                    lastName,\n                                                                                    email: session.user.email || '',\n                                                                                    jobTitle: 'Project Manager',\n                                                                                    department: '',\n                                                                                    departmentId: '',\n                                                                                    phone: '',\n                                                                                    bio: \"\".concat(session.user.role || 'Team member', \" with expertise in project management.\")\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: \"Reset\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        className: \"btn btn-primary\",\n                                                                        disabled: isLoading || !profile.firstName.trim() || !profile.lastName.trim(),\n                                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                            className: \"opacity-25\",\n                                                                                            cx: \"12\",\n                                                                                            cy: \"12\",\n                                                                                            r: \"10\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            strokeWidth: \"4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 529,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            className: \"opacity-75\",\n                                                                                            fill: \"currentColor\",\n                                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 530,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Saving...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 527,\n                                                                            columnNumber: 29\n                                                                        }, this) : 'Save Changes'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    {\n                                                        title: 'Email Notifications',\n                                                        desc: 'Receive notifications via email'\n                                                    },\n                                                    {\n                                                        title: 'Project Updates',\n                                                        desc: 'Get notified when projects are updated'\n                                                    },\n                                                    {\n                                                        title: 'Task Assignments',\n                                                        desc: 'Notifications for new task assignments'\n                                                    },\n                                                    {\n                                                        title: 'Deadline Reminders',\n                                                        desc: 'Reminders for upcoming deadlines'\n                                                    },\n                                                    {\n                                                        title: 'Team Mentions',\n                                                        desc: 'When someone mentions you in comments'\n                                                    }\n                                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-4 border-b border-slate-200 dark:border-slate-700 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                        children: item.desc\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"sr-only peer\",\n                                                                        defaultChecked: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'appearance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Appearance Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    {\n                                                                        name: 'Light',\n                                                                        desc: 'Clean and bright interface',\n                                                                        value: 'light'\n                                                                    },\n                                                                    {\n                                                                        name: 'Dark',\n                                                                        desc: 'Easy on the eyes',\n                                                                        value: 'dark'\n                                                                    },\n                                                                    {\n                                                                        name: 'Auto',\n                                                                        desc: 'Matches system preference',\n                                                                        value: 'auto'\n                                                                    }\n                                                                ].map((themeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card p-4 cursor-pointer border-2 \".concat(theme === themeOption.value ? 'border-blue-500' : 'border-transparent', \" dark:bg-slate-800\"),\n                                                                        onClick: ()=>handleThemeChange(themeOption.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                children: themeOption.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                children: themeOption.desc\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, themeOption.value, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Display Options\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"Compact Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 599,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Reduce spacing for more content\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: compactMode,\n                                                                                        onChange: handleCompactModeToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 603,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 609,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 597,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"High Contrast\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 615,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Increase contrast for better visibility\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: highContrast,\n                                                                                        onChange: handleHighContrastToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 619,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 625,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Change Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                                onSubmit: handlePasswordUpdate,\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Current Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 642,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: currentPassword,\n                                                                                onChange: (e)=>setCurrentPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 652,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: newPassword,\n                                                                                onChange: (e)=>setNewPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 653,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 651,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Confirm New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: confirmPassword,\n                                                                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 663,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-red-500 text-sm\",\n                                                                        children: passwordError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 673,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"submit\",\n                                                                                className: \"btn btn-primary\",\n                                                                                disabled: isLoading,\n                                                                                children: isLoading ? 'Updating...' : 'Update Password'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"btn btn-secondary\",\n                                                                                onClick: handlePasswordReset,\n                                                                                children: \"Send Reset Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 684,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Login Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-600 dark:text-slate-400 mb-4\",\n                                                                children: \"Monitor your account login activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-slate-50 dark:bg-slate-700 p-3 rounded-lg mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-900 dark:text-white font-medium\",\n                                                                        children: \"Current Session\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 699,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            \"Active now - \",\n                                                                            new Date().toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 698,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"btn btn-secondary text-sm\",\n                                                                children: \"View All Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'integrations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    {\n                                                        name: 'Slack',\n                                                        desc: 'Connect with your Slack workspace',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Microsoft Teams',\n                                                        desc: 'Integrate with Teams for notifications',\n                                                        connected: false\n                                                    },\n                                                    {\n                                                        name: 'Google Calendar',\n                                                        desc: 'Sync project deadlines with calendar',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Jira',\n                                                        desc: 'Import and sync Jira issues',\n                                                        connected: false\n                                                    }\n                                                ].map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                                            children: integration.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                            children: integration.desc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 722,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn \".concat(integration.connected ? 'btn-secondary' : 'btn-primary'),\n                                                                    children: integration.connected ? 'Disconnect' : 'Connect'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 299,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"IysQmi3MYF+YSWwMev/AVTT0jvw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});