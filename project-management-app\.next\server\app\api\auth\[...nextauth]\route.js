/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbW9oZDlcXERvd25sb2Fkc1xccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcc3JjXFxhcHBcXGFwaVxcYXV0aFxcWy4uLm5leHRhdXRoXVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnQC9saWIvYXV0aCc7XHJcblxyXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xyXG5cclxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9OyAiXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n\n\n// Special case for development mode\nconst handleDevAuth = (email, password)=>{\n    if (true) {\n        // Default admin credentials\n        if (email === '<EMAIL>' && password === 'password') {\n            return {\n                id: 'admin',\n                name: 'Admin User',\n                email: '<EMAIL>',\n                role: 'ADMIN'\n            };\n        }\n        // Additional development user for AOPS\n        if (email === '<EMAIL>' && password === 'password') {\n            return {\n                id: 'aops-admin',\n                name: 'AOPS Admin',\n                email: '<EMAIL>',\n                role: 'ADMIN'\n            };\n        }\n    }\n    return null;\n};\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'text'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // First check for dev credentials\n                const devUser = handleDevAuth(credentials.email, credentials.password);\n                if (devUser) return devUser;\n                try {\n                    // Sign in with Firebase Auth\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password);\n                    const uid = userCredential.user.uid;\n                    // Get additional user data from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid));\n                    if (!userDoc.exists()) {\n                        // Create a basic user record if it doesn't exist\n                        const userData = {\n                            name: credentials.email.split('@')[0],\n                            email: credentials.email,\n                            role: 'USER',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), userData);\n                        return {\n                            id: uid,\n                            email: credentials.email,\n                            name: userData.name,\n                            role: userData.role\n                        };\n                    }\n                    const userData = userDoc.data();\n                    return {\n                        id: uid,\n                        email: credentials.email,\n                        name: userData.name || credentials.email.split('@')[0],\n                        role: userData.role || 'USER'\n                    };\n                } catch (error) {\n                    console.error('Firebase auth error:', error);\n                    // For development, create a fallback admin user\n                    if ( true && (credentials.email === '<EMAIL>' && credentials.password === 'password' || credentials.email === '<EMAIL>' && credentials.password === 'password')) {\n                        try {\n                            // Try to create the admin user in Firebase\n                            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password).then(async (userCredential)=>{\n                                const uid = userCredential.user.uid;\n                                // Create admin user in Firestore\n                                const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';\n                                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), {\n                                    name: userName,\n                                    email: credentials.email,\n                                    role: 'ADMIN',\n                                    department: 'Admin',\n                                    createdAt: new Date().toISOString(),\n                                    updatedAt: new Date().toISOString()\n                                });\n                            }).catch((err)=>{\n                                console.log('Admin user may already exist:', err);\n                            });\n                            const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';\n                            const userId = credentials.email === '<EMAIL>' ? 'aops-admin' : 'admin';\n                            return {\n                                id: userId,\n                                name: userName,\n                                email: credentials.email,\n                                role: 'ADMIN'\n                            };\n                        } catch (createError) {\n                            console.error('Could not create admin user:', createError);\n                            return null;\n                        }\n                    }\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        jwt: async ({ token, user })=>{\n            if (user) {\n                token.role = user.role;\n                token.sub = user.id;\n            }\n            return token;\n        },\n        session: async ({ session, token })=>{\n            if (token && session.user) {\n                session.user.role = token.role;\n                session.user.id = token.sub;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    debug: \"development\" === 'development'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n// Import the functions you need from the SDKs you need\n\n\n\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k\",\n    authDomain: \"project-management-f45cc.firebaseapp.com\",\n    projectId: \"project-management-f45cc\",\n    storageBucket: \"project-management-f45cc.firebasestorage.app\",\n    messagingSenderId: \"1002222709659\",\n    appId: \"1:1002222709659:web:6b1ab479efcc4102824f3e\",\n    measurementId: \"G-JYYNYZV8LP\"\n};\n// Initialize Firebase only if we don't already have an instance\n// This helps prevent multiple initializations during SSR/SSG\nlet app;\nif (!(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Analytics is now null by default\nconst analytics = null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/lru-cache","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();