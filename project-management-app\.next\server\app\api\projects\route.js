/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/route";
exports.ids = ["app/api/projects/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/route.ts */ \"(rsc)/./src/app/api/projects/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/route\",\n        pathname: \"/api/projects\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\api\\\\projects\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/projects/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/projects/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_projectService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/projectService */ \"(rsc)/./src/services/projectService.ts\");\n/* harmony import */ var _services_projectLogService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/projectLogService */ \"(rsc)/./src/services/projectLogService.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n\n\n\n// GET /api/projects - Get all projects\nasync function GET() {\n    try {\n        const projects = await (0,_services_projectService__WEBPACK_IMPORTED_MODULE_1__.getAllProjects)();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(projects);\n    } catch (error) {\n        console.error('Error fetching projects:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch projects'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/projects - Create a new project\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_3__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_4__.authOptions);\n        // Validate required fields\n        const requiredFields = [\n            'projectTitle',\n            'drivers',\n            'type',\n            'opdFocal',\n            'department',\n            'area'\n        ];\n        for (const field of requiredFields){\n            if (!body[field] || body[field].trim() === '') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Convert numeric values if they come as strings\n        const projectData = {\n            ...body,\n            year: typeof body.year === 'string' ? parseInt(body.year, 10) : body.year,\n            percentage: typeof body.percentage === 'string' ? parseInt(body.percentage, 10) : body.percentage,\n            budget: body.budget ? parseFloat(body.budget) : null,\n            awardAmount: body.awardAmount ? parseFloat(body.awardAmount) : null,\n            savingsOMR: body.savingsOMR ? parseFloat(body.savingsOMR) : null,\n            savingsPercentage: body.savingsPercentage ? parseFloat(body.savingsPercentage) : null,\n            duration: body.duration ? parseInt(body.duration, 10) : null,\n            createdBy: session?.user?.id || ''\n        };\n        const project = await (0,_services_projectService__WEBPACK_IMPORTED_MODULE_1__.createProject)(projectData);\n        // Log project creation if user is authenticated\n        if (session?.user && project.id) {\n            await (0,_services_projectLogService__WEBPACK_IMPORTED_MODULE_2__.logProjectCreation)(project.id, project.projectTitle, session.user.id || '', session.user.name || '');\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(project);\n    } catch (error) {\n        console.error('Error creating project:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create project'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n\n\n// Special case for development mode\nconst handleDevAuth = (email, password)=>{\n    if ( true && email === '<EMAIL>' && password === 'password') {\n        return {\n            id: 'admin',\n            name: 'Admin User',\n            email: '<EMAIL>',\n            role: 'ADMIN'\n        };\n    }\n    return null;\n};\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'text'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // First check for dev credentials\n                const devUser = handleDevAuth(credentials.email, credentials.password);\n                if (devUser) return devUser;\n                try {\n                    // Sign in with Firebase Auth\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password);\n                    const uid = userCredential.user.uid;\n                    // Get additional user data from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid));\n                    if (!userDoc.exists()) {\n                        // Create a basic user record if it doesn't exist\n                        const userData = {\n                            name: credentials.email.split('@')[0],\n                            email: credentials.email,\n                            role: 'USER',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), userData);\n                        return {\n                            id: uid,\n                            email: credentials.email,\n                            name: userData.name,\n                            role: userData.role\n                        };\n                    }\n                    const userData = userDoc.data();\n                    return {\n                        id: uid,\n                        email: credentials.email,\n                        name: userData.name || credentials.email.split('@')[0],\n                        role: userData.role || 'USER'\n                    };\n                } catch (error) {\n                    console.error('Firebase auth error:', error);\n                    // For development, create a fallback admin user\n                    if ( true && credentials.email === '<EMAIL>' && credentials.password === 'password') {\n                        try {\n                            // Try to create the admin user in Firebase\n                            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password).then(async (userCredential)=>{\n                                const uid = userCredential.user.uid;\n                                // Create admin user in Firestore\n                                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), {\n                                    name: 'Admin User',\n                                    email: credentials.email,\n                                    role: 'ADMIN',\n                                    department: 'Admin',\n                                    createdAt: new Date().toISOString(),\n                                    updatedAt: new Date().toISOString()\n                                });\n                            }).catch((err)=>{\n                                console.log('Admin user may already exist:', err);\n                            });\n                            return {\n                                id: 'admin',\n                                name: 'Admin User',\n                                email: '<EMAIL>',\n                                role: 'ADMIN'\n                            };\n                        } catch (createError) {\n                            console.error('Could not create admin user:', createError);\n                            return null;\n                        }\n                    }\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        jwt: async ({ token, user })=>{\n            if (user) {\n                token.role = user.role;\n                token.sub = user.id;\n            }\n            return token;\n        },\n        session: async ({ session, token })=>{\n            if (token && session.user) {\n                session.user.role = token.role;\n                session.user.id = token.sub;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    debug: \"development\" === 'development'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n// Import the functions you need from the SDKs you need\n\n\n\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k\",\n    authDomain: \"project-management-f45cc.firebaseapp.com\",\n    projectId: \"project-management-f45cc\",\n    storageBucket: \"project-management-f45cc.firebasestorage.app\",\n    messagingSenderId: \"1002222709659\",\n    appId: \"1:1002222709659:web:6b1ab479efcc4102824f3e\",\n    measurementId: \"G-JYYNYZV8LP\"\n};\n// Initialize Firebase only if we don't already have an instance\n// This helps prevent multiple initializations during SSR/SSG\nlet app;\nif (!(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Analytics is now null by default\nconst analytics = null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/projectLogService.ts":
/*!*******************************************!*\
  !*** ./src/services/projectLogService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createProjectLog: () => (/* binding */ createProjectLog),\n/* harmony export */   deleteAllProjectLogs: () => (/* binding */ deleteAllProjectLogs),\n/* harmony export */   deleteProjectLog: () => (/* binding */ deleteProjectLog),\n/* harmony export */   getAllProjectLogs: () => (/* binding */ getAllProjectLogs),\n/* harmony export */   getProjectLogs: () => (/* binding */ getProjectLogs),\n/* harmony export */   logProjectCreation: () => (/* binding */ logProjectCreation),\n/* harmony export */   logProjectUpdate: () => (/* binding */ logProjectUpdate),\n/* harmony export */   logStatusChange: () => (/* binding */ logStatusChange),\n/* harmony export */   logSubStatusChange: () => (/* binding */ logSubStatusChange)\n/* harmony export */ });\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Local implementation of toJsDate\nfunction toJsDate(date) {\n    if (!date) return null;\n    try {\n        // Already a Date object\n        if (date instanceof Date) {\n            return date;\n        }\n        // String date (ISO format or other string representation)\n        if (typeof date === 'string') {\n            // Handle ISO dates\n            if (date.match(/^\\d{4}-\\d{2}-\\d{2}/) || date.includes('T00:00:00')) {\n                const parsedDate = new Date(date);\n                if (!isNaN(parsedDate.getTime())) {\n                    return parsedDate;\n                }\n            }\n            // Try to parse as a date anyway\n            const parsedDate = new Date(date);\n            if (!isNaN(parsedDate.getTime())) {\n                return parsedDate;\n            }\n        }\n        // Numeric timestamp (milliseconds since epoch)\n        if (typeof date === 'number') {\n            return new Date(date);\n        }\n        // Firebase Timestamp with toDate() method\n        if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {\n            return date.toDate();\n        }\n        // Firebase Timestamp-like object with seconds and nanoseconds\n        if (date && typeof date === 'object' && 'seconds' in date) {\n            return new Date(date.seconds * 1000);\n        }\n        // Stringified object that might contain a timestamp\n        if (typeof date === 'string' && (date.includes('\"seconds\"') || date.includes('\"nanoseconds\"'))) {\n            try {\n                const parsed = JSON.parse(date);\n                if (parsed && typeof parsed === 'object' && 'seconds' in parsed) {\n                    return new Date(parsed.seconds * 1000);\n                }\n            } catch (e) {}\n        }\n    } catch (error) {\n        console.error('Error converting to JS Date:', error);\n    }\n    return null;\n}\n// Convert Firestore document to ProjectLog object\nconst projectLogConverter = {\n    fromFirestore (snapshot) {\n        const data = snapshot.data();\n        // Ensure we have a valid date for createdAt\n        let createdAt = new Date();\n        try {\n            if (data.createdAt) {\n                const dateFromFirestore = toJsDate(data.createdAt);\n                if (dateFromFirestore && !isNaN(dateFromFirestore.getTime())) {\n                    createdAt = dateFromFirestore;\n                }\n            }\n        } catch (error) {\n            console.warn(`Error converting createdAt for log ${snapshot.id}:`, error);\n        }\n        // Ensure changes is a properly structured object\n        let changes = {};\n        try {\n            if (data.changes && typeof data.changes === 'object') {\n                changes = Object.entries(data.changes).reduce((acc, [key, value])=>{\n                    const change = value;\n                    if (change && typeof change === 'object' && ('from' in change || 'to' in change)) {\n                        // Handle Firebase Timestamp objects in from/to values\n                        let fromValue = change.from;\n                        let toValue = change.to;\n                        // Don't convert Firebase timestamps to avoid issues\n                        if (typeof fromValue === 'object' && fromValue !== null && 'seconds' in fromValue) {\n                        // Keep as is\n                        }\n                        if (typeof toValue === 'object' && toValue !== null && 'seconds' in toValue) {\n                        // Keep as is\n                        }\n                        acc[key] = {\n                            from: fromValue !== undefined ? fromValue : '',\n                            to: toValue !== undefined ? toValue : ''\n                        };\n                    }\n                    return acc;\n                }, {});\n            }\n        } catch (error) {\n            console.warn(`Error processing changes for log ${snapshot.id}:`, error);\n        }\n        return {\n            id: snapshot.id,\n            projectId: data.projectId || '',\n            action: data.action || 'PROJECT_UPDATED',\n            description: data.description || '',\n            changes: changes,\n            note: data.note || '',\n            createdBy: data.createdBy || '',\n            createdByName: data.createdByName || '',\n            createdAt\n        };\n    },\n    toFirestore (log) {\n        return {\n            projectId: log.projectId,\n            action: log.action,\n            description: log.description,\n            changes: log.changes || {},\n            note: log.note || '',\n            createdBy: log.createdBy,\n            createdByName: log.createdByName,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n    }\n};\n// Collection reference\nconst projectLogsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projectLogs');\n// Create new project log\nasync function createProjectLog(logData) {\n    try {\n        console.log('Creating log with data:', JSON.stringify(logData, null, 2));\n        // Validate required fields\n        if (!logData.projectId) {\n            throw new Error('projectId is required');\n        }\n        if (!logData.action) {\n            throw new Error('action is required');\n        }\n        if (!logData.description) {\n            throw new Error('description is required');\n        }\n        // Ensure changes is an object\n        const changes = logData.changes || {};\n        // Ensure dates are properly converted\n        const data = projectLogConverter.toFirestore({\n            ...logData,\n            changes\n        });\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(projectLogsCollection, data);\n        console.log('Log created with ID:', docRef.id);\n        // Return the created log (we'll simulate the timestamp for immediate return)\n        return {\n            ...logData,\n            id: docRef.id,\n            createdAt: new Date()\n        };\n    } catch (error) {\n        console.error('Error creating project log:', error);\n        throw error;\n    }\n}\n// Get logs for a specific project\nasync function getProjectLogs(projectId) {\n    try {\n        console.log('Fetching logs for project ID:', projectId);\n        // First try with a simpler query that doesn't require a composite index\n        try {\n            const simpleQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectLogsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('projectId', '==', projectId));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(simpleQuery);\n            console.log(`Found ${snapshot.docs.length} logs for project ${projectId}`);\n            // Convert and sort on the client side\n            const logs = snapshot.docs.map((doc)=>projectLogConverter.fromFirestore(doc));\n            return logs.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime());\n        } catch (simpleQueryError) {\n            console.warn('Simple query failed, attempting with composite index:', simpleQueryError);\n            // Fall back to the original query with orderBy which requires a composite index\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectLogsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('projectId', '==', projectId), (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('createdAt', 'desc'));\n            const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n            console.log(`Found ${snapshot.docs.length} logs for project ${projectId}`);\n            return snapshot.docs.map((doc)=>projectLogConverter.fromFirestore(doc));\n        }\n    } catch (error) {\n        console.error(`Error getting logs for project ${projectId}:`, error);\n        // Enhanced error handling for index errors\n        if (error instanceof Error && error.message.includes('index')) {\n            console.error('Index error detected. Please create the required Firestore index by following the link in the error message above.');\n            console.error('After creating the index, it may take a few minutes to become active.');\n        }\n        throw error;\n    }\n}\n// Get all logs (for admin view)\nasync function getAllProjectLogs() {\n    try {\n        console.log('Fetching all project logs');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectLogsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('createdAt', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        console.log(`Found ${snapshot.docs.length} total project logs`);\n        return snapshot.docs.map((doc)=>projectLogConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error('Error getting all project logs:', error);\n        throw error;\n    }\n}\n// Helper function to create status change log\nasync function logStatusChange(projectId, fromStatus, toStatus, note, userId, userName) {\n    try {\n        console.log(`Logging status change for project ${projectId} from ${fromStatus} to ${toStatus}`);\n        const logData = {\n            projectId,\n            action: 'STATUS_CHANGE',\n            description: `Status changed from \"${fromStatus}\" to \"${toStatus}\"`,\n            changes: {\n                status: {\n                    from: fromStatus,\n                    to: toStatus\n                }\n            },\n            note,\n            createdBy: userId,\n            createdByName: userName\n        };\n        return createProjectLog(logData);\n    } catch (error) {\n        console.error(`Error logging status change for project ${projectId}:`, error);\n        throw error;\n    }\n}\n// Helper function to create sub-status change log\nasync function logSubStatusChange(projectId, fromSubStatus, toSubStatus, userId, userName) {\n    try {\n        console.log(`Logging sub-status change for project ${projectId} from ${fromSubStatus} to ${toSubStatus}`);\n        const logData = {\n            projectId,\n            action: 'SUBSTATUS_CHANGE',\n            description: `Sub-status changed from \"${fromSubStatus}\" to \"${toSubStatus}\"`,\n            changes: {\n                subStatus: {\n                    from: fromSubStatus,\n                    to: toSubStatus\n                }\n            },\n            createdBy: userId,\n            createdByName: userName\n        };\n        return createProjectLog(logData);\n    } catch (error) {\n        console.error(`Error logging sub-status change for project ${projectId}:`, error);\n        throw error;\n    }\n}\n// Helper function to log project creation\nasync function logProjectCreation(projectId, projectTitle, userId, userName) {\n    try {\n        console.log(`Logging project creation for project ${projectId} - ${projectTitle}`);\n        const logData = {\n            projectId,\n            action: 'PROJECT_CREATED',\n            description: `Project \"${projectTitle}\" was created`,\n            changes: {},\n            createdBy: userId,\n            createdByName: userName\n        };\n        return createProjectLog(logData);\n    } catch (error) {\n        console.error(`Error logging project creation for ${projectId}:`, error);\n        throw error;\n    }\n}\n// Helper function to log general project updates\nasync function logProjectUpdate(projectId, description, changes, userId, userName) {\n    try {\n        console.log(`Logging project update for project ${projectId}: ${description}`);\n        const logData = {\n            projectId,\n            action: 'PROJECT_UPDATED',\n            description,\n            changes,\n            createdBy: userId,\n            createdByName: userName\n        };\n        return createProjectLog(logData);\n    } catch (error) {\n        console.error(`Error logging project update for ${projectId}:`, error);\n        throw error;\n    }\n}\n// Delete a specific log entry\nasync function deleteProjectLog(logId) {\n    try {\n        console.log(`Deleting log with ID: ${logId}`);\n        const logRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projectLogs', logId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)(logRef);\n        console.log(`Log ${logId} deleted successfully`);\n        return true;\n    } catch (error) {\n        console.error(`Error deleting log ${logId}:`, error);\n        throw error;\n    }\n}\n// Delete all logs for a specific project\nasync function deleteAllProjectLogs(projectId) {\n    try {\n        console.log(`Deleting all logs for project ID: ${projectId}`);\n        // Get all logs for the project\n        const logs = await getProjectLogs(projectId);\n        // Delete each log\n        const deletePromises = logs.map((log)=>deleteProjectLog(log.id));\n        await Promise.all(deletePromises);\n        console.log(`Deleted ${logs.length} logs for project ${projectId}`);\n        return logs.length;\n    } catch (error) {\n        console.error(`Error deleting all logs for project ${projectId}:`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/projectLogService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/projectService.ts":
/*!****************************************!*\
  !*** ./src/services/projectService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createProject: () => (/* binding */ createProject),\n/* harmony export */   deleteProject: () => (/* binding */ deleteProject),\n/* harmony export */   getAllProjects: () => (/* binding */ getAllProjects),\n/* harmony export */   getProjectById: () => (/* binding */ getProjectById),\n/* harmony export */   getProjectsByDepartment: () => (/* binding */ getProjectsByDepartment),\n/* harmony export */   getProjectsByStatus: () => (/* binding */ getProjectsByStatus),\n/* harmony export */   searchProjects: () => (/* binding */ searchProjects),\n/* harmony export */   updateProject: () => (/* binding */ updateProject)\n/* harmony export */ });\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Define our own toJsDate function to prevent dependency issues\nfunction toJsDate(date) {\n    if (!date) return null;\n    try {\n        // Already a Date object\n        if (date instanceof Date) {\n            return date;\n        }\n        // String date (ISO format or other string representation)\n        if (typeof date === 'string') {\n            // Handle ISO dates\n            if (date.match(/^\\d{4}-\\d{2}-\\d{2}/) || date.includes('T00:00:00')) {\n                const parsedDate = new Date(date);\n                if (!isNaN(parsedDate.getTime())) {\n                    return parsedDate;\n                }\n            }\n            // Try to parse as a date anyway\n            const parsedDate = new Date(date);\n            if (!isNaN(parsedDate.getTime())) {\n                return parsedDate;\n            }\n        }\n        // Numeric timestamp (milliseconds since epoch)\n        if (typeof date === 'number') {\n            return new Date(date);\n        }\n        // Firebase Timestamp with toDate() method\n        if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {\n            return date.toDate();\n        }\n        // Firebase Timestamp-like object with seconds and nanoseconds\n        if (date && typeof date === 'object' && 'seconds' in date) {\n            return new Date(date.seconds * 1000);\n        }\n        // Stringified object that might contain a timestamp\n        if (typeof date === 'string' && (date.includes('\"seconds\"') || date.includes('\"nanoseconds\"'))) {\n            try {\n                const parsed = JSON.parse(date);\n                if (parsed && typeof parsed === 'object' && 'seconds' in parsed) {\n                    return new Date(parsed.seconds * 1000);\n                }\n            } catch (e) {}\n        }\n    } catch (error) {\n        console.error('Error converting to JS Date:', error);\n    }\n    return null;\n}\n// Convert Firestore document to Project object\nconst projectConverter = {\n    fromFirestore (snapshot) {\n        const data = snapshot.data();\n        // Handle date conversions\n        const createdAt = toJsDate(data.createdAt) || new Date();\n        const updatedAt = toJsDate(data.updatedAt) || new Date();\n        const startDate = data.startDate ? toJsDate(data.startDate) : null;\n        const completionDate = data.completionDate ? toJsDate(data.completionDate) : null;\n        const dateOfReceiveFinalDoc = data.dateOfReceiveFinalDoc ? toJsDate(data.dateOfReceiveFinalDoc) : null;\n        const statusChangeDate = data.statusChangeDate ? toJsDate(data.statusChangeDate) : null;\n        return {\n            id: snapshot.id,\n            projectTitle: data.projectTitle || '',\n            department: data.department || '',\n            status: data.status || 'Possible',\n            subStatus: data.subStatus || '',\n            completionDate,\n            startDate,\n            percentage: data.percentage || 0,\n            budget: data.budget || 0,\n            awardAmount: data.awardAmount || 0,\n            awardedCompany: data.awardedCompany || '',\n            savingsOMR: data.savingsOMR || 0,\n            savingsPercentage: data.savingsPercentage || 0,\n            drivers: data.drivers || '',\n            type: data.type || '',\n            opdFocal: data.opdFocal || '',\n            area: data.area || '',\n            capexOpex: data.capexOpex || 'CAPEX',\n            year: data.year || new Date().getFullYear(),\n            briefStatus: data.briefStatus || '',\n            pr: data.pr || '',\n            duration: data.duration || 0,\n            poNumber: data.poNumber || '',\n            pmoNumber: data.pmoNumber || '',\n            column1: data.column1 || '',\n            dateOfReceiveFinalDoc,\n            quarterOfYear: data.quarterOfYear || 'Q1',\n            createdAt,\n            updatedAt,\n            createdBy: data.createdBy || '',\n            departmentId: data.departmentId || '',\n            statusChangeNote: data.statusChangeNote || '',\n            statusChangeDate\n        };\n    },\n    toFirestore (project) {\n        const data = {\n            ...project,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        // Remove id field if present\n        delete data.id;\n        return data;\n    }\n};\n// Collection reference\nconst projectsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects');\n// Get all projects\nasync function getAllProjects() {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.orderBy)('createdAt', 'desc'));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>projectConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error('Error getting projects:', error);\n        throw error;\n    }\n}\n// Get project by ID\nasync function getProjectById(id) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects', id);\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!snapshot.exists()) {\n            return null;\n        }\n        return projectConverter.fromFirestore(snapshot);\n    } catch (error) {\n        console.error(`Error getting project ${id}:`, error);\n        throw error;\n    }\n}\n// Get projects by department\nasync function getProjectsByDepartment(department) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('department', '==', department));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>projectConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting projects for department ${department}:`, error);\n        throw error;\n    }\n}\n// Get projects by status\nasync function getProjectsByStatus(status) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(projectsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('status', '==', status));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>projectConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting projects with status ${status}:`, error);\n        throw error;\n    }\n}\n// Create new project\nasync function createProject(projectData) {\n    try {\n        const data = {\n            ...projectData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(projectsCollection, data);\n        const newDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        return projectConverter.fromFirestore(newDoc);\n    } catch (error) {\n        console.error('Error creating project:', error);\n        throw error;\n    }\n}\n// Update project\nasync function updateProject(id, projectData) {\n    try {\n        console.log(`Updating project ${id} with data:`, JSON.stringify(projectData, null, 2));\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects', id);\n        // Validate that document exists before updating\n        const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!docSnap.exists()) {\n            throw new Error(`Cannot update: Project with ID ${id} not found`);\n        }\n        // Clean the data to prevent Invalid DocumentReference errors\n        const cleanedData = {\n            ...projectData\n        };\n        // Add updatedAt timestamp\n        const data = projectConverter.toFirestore(cleanedData);\n        console.log('Converted data for Firestore:', JSON.stringify(data, null, 2));\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(docRef, data);\n        console.log(`Project ${id} updated successfully`);\n        // Get updated document\n        const updatedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!updatedDoc.exists()) {\n            throw new Error(`Project with ID ${id} not found after update`);\n        }\n        return projectConverter.fromFirestore(updatedDoc);\n    } catch (error) {\n        console.error(`Error updating project ${id}:`, error);\n        throw error;\n    }\n}\n// Delete project\nasync function deleteProject(id) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'projects', id));\n    } catch (error) {\n        console.error(`Error deleting project ${id}:`, error);\n        throw error;\n    }\n}\n// Search projects\nasync function searchProjects(searchTerm) {\n    try {\n        // For now, we'll get all projects and filter client-side\n        // Firebase doesn't support full-text search natively\n        const allProjects = await getAllProjects();\n        const lowerSearchTerm = searchTerm.toLowerCase();\n        return allProjects.filter((project)=>project.projectTitle.toLowerCase().includes(lowerSearchTerm) || project.department.toLowerCase().includes(lowerSearchTerm) || project.opdFocal.toLowerCase().includes(lowerSearchTerm) || project.area.toLowerCase().includes(lowerSearchTerm) || project.status.toLowerCase().includes(lowerSearchTerm));\n    } catch (error) {\n        console.error('Error searching projects:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/projectService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/next-auth","vendor-chunks/long","vendor-chunks/@babel","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();