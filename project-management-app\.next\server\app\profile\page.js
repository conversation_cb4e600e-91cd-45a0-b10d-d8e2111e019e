/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.tsx */ \"(rsc)/./src/app/profile/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNtb2hkOSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNwcm9qZWN0LW1hbmFnZW1lbnQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBd0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGQ5XFxcXERvd25sb2Fkc1xcXFxwcm9qZWN0LW1hbmFnZW1lbnQtYXBwXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.tsx */ \"(rsc)/./src/app/profile/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9maWxlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oZDlcXFxcRG93bmxvYWRzXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXHByb2ZpbGVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbW9oZDlcXERvd25sb2Fkc1xccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a52225d7d09e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vaGQ5XFxEb3dubG9hZHNcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE1MjIyNWQ3ZDA5ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'TaskMaster - Project Management',\n    description: 'A modern project management application for teams'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full bg-slate-50 text-slate-900 antialiased`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQXVCO0FBS2pCQTtBQUZrQztBQUlqQyxNQUFNRSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLFdBQVU7a0JBQ3hCLDRFQUFDQztZQUFLRCxXQUFXLEdBQUdULCtKQUFlLENBQUMsOENBQThDLENBQUM7WUFBRVcsd0JBQXdCO3NCQUMzRyw0RUFBQ1YsaURBQVNBOzBCQUNQSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vaGQ5XFxEb3dubG9hZHNcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnLi9wcm92aWRlcnMnO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1Rhc2tNYXN0ZXIgLSBQcm9qZWN0IE1hbmFnZW1lbnQnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHByb2plY3QgbWFuYWdlbWVudCBhcHBsaWNhdGlvbiBmb3IgdGVhbXMnLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT1cImgtZnVsbFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGgtZnVsbCBiZy1zbGF0ZS01MCB0ZXh0LXNsYXRlLTkwMCBhbnRpYWxpYXNlZGB9IHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgICAgPFByb3ZpZGVycz5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlByb3ZpZGVycyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwiYm9keSIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\project-management-app\\project-management-app\\src\\app\\profile\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Downloads\\project-management-app\\project-management-app\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNtb2hkOSU1QyU1Q0Rvd25sb2FkcyU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNwcm9qZWN0LW1hbmFnZW1lbnQtYXBwJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBd0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vaGQ5XFxcXERvd25sb2Fkc1xcXFxwcm9qZWN0LW1hbmFnZW1lbnQtYXBwXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/profile/page.tsx */ \"(ssr)/./src/app/profile/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vaGQ5JTVDJTVDRG93bmxvYWRzJTVDJTVDcHJvamVjdC1tYW5hZ2VtZW50LWFwcCU1QyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwcm9maWxlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbW9oZDlcXFxcRG93bmxvYWRzXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXHByb2ZpbGVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmohd9%5C%5CDownloads%5C%5Cproject-management-app%5C%5Cproject-management-app%5C%5Csrc%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AppLayout */ \"(ssr)/./src/components/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ProfilePage() {\n    const { data: session, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [department, setDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        text: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfilePage.useEffect\": ()=>{\n            if (session?.user) {\n                setName(session.user.name || '');\n                setEmail(session.user.email || '');\n                setRole(session.user.role || 'User');\n                setDepartment(session.user.department || '');\n            }\n        }\n    }[\"ProfilePage.useEffect\"], [\n        session\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSaving(true);\n        setMessage({\n            type: '',\n            text: ''\n        });\n        try {\n            // In a real app, you would update the user profile via an API call\n            // const response = await fetch('/api/user/profile', {\n            //   method: 'PUT',\n            //   headers: { 'Content-Type': 'application/json' },\n            //   body: JSON.stringify({ name, email, role, department }),\n            // });\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Update the session\n            await update({\n                ...session,\n                user: {\n                    ...session?.user,\n                    name,\n                    email\n                }\n            });\n            setIsEditing(false);\n            setMessage({\n                type: 'success',\n                text: 'Profile updated successfully!'\n            });\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            setMessage({\n                type: 'error',\n                text: 'Failed to update profile. Please try again.'\n            });\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Your Profile\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-3xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-slate-800 shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-8 border-b border-slate-200 dark:border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-20 w-20 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center text-2xl font-bold text-white\",\n                                    children: name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                                            children: name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-400\",\n                                            children: role\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this),\n                                        department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-400\",\n                                            children: [\n                                                \"Department: \",\n                                                department\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `mb-6 p-4 rounded-lg ${message.type === 'success' ? 'bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-50 text-red-800 dark:bg-red-900/30 dark:text-red-300'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        message.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 w-5 mr-2 text-red-500\",\n                                            children: \"!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 21\n                                        }, this),\n                                        message.text\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Full Name\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"name\",\n                                                        name: \"name\",\n                                                        value: name,\n                                                        onChange: (e)=>setName(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Email\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"role\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Role\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"role\",\n                                                        name: \"role\",\n                                                        value: role,\n                                                        onChange: (e)=>setRole(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"User\",\n                                                                children: \"User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Admin\",\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PMO\",\n                                                                children: \"PMO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex justify-end\",\n                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setIsEditing(false),\n                                                    className: \"px-4 py-2 border border-slate-300 text-slate-700 dark:text-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 mr-3\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSaving,\n                                                    className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-70 disabled:cursor-not-allowed\",\n                                                    children: isSaving ? 'Saving...' : 'Save Changes'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsEditing(true),\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                                            children: \"Edit Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/profile/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtEO0FBRTNDLFNBQVNDLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxxQkFBTyw4REFBQ0YsNERBQWVBO2tCQUFFRTs7Ozs7O0FBQzNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vaGQ5XFxEb3dubG9hZHNcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHNyY1xcYXBwXFxwcm92aWRlcnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICByZXR1cm4gPFNlc3Npb25Qcm92aWRlcj57Y2hpbGRyZW59PC9TZXNzaW9uUHJvdmlkZXI+O1xyXG59ICJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AppLayout.tsx":
/*!**************************************!*\
  !*** ./src/components/AppLayout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOfficeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,Bars3Icon,BuildingOfficeIcon,ChartBarIcon,ClipboardDocumentListIcon,Cog6ToothIcon,HomeIcon,UserCircleIcon,UserGroupIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _NotificationCenter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./NotificationCenter */ \"(ssr)/./src/components/NotificationCenter.tsx\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n// Define the navigation items\nconst navigation = [\n    {\n        name: 'Dashboard',\n        href: '/',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Projects',\n        href: '/projects',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Bulk Import',\n        href: '/projects/bulk-import',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: 'Team',\n        href: '/team',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        name: 'Departments',\n        href: '/departments',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        name: 'Analytics',\n        href: '/analytics',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    },\n    {\n        name: 'Activity Logs',\n        href: '/logs',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Settings',\n        href: '/settings',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    }\n];\nfunction AppLayout({ children, title }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const isActive = (href)=>{\n        return pathname === href;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 dark:bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"bottom-right\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 sticky top-0 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between h-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"px-4 border-r border-slate-200 dark:border-slate-700 text-slate-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden\",\n                                                onClick: ()=>setSidebarOpen(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open sidebar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center flex-shrink-0 px-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/\",\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"h-8 w-8 rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"TM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                            children: \"TaskMaster\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationCenter__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"flex max-w-xs items-center rounded-full bg-white dark:bg-slate-800 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2\",\n                                                        onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sr-only\",\n                                                                children: \"Open user menu\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            session?.user?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                className: \"h-8 w-8 rounded-full\",\n                                                                src: session.user.image,\n                                                                alt: \"User profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: session?.user?.name?.charAt(0) || 'U'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                    lineNumber: 99,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute right-0 mt-2 w-48 rounded-md bg-white dark:bg-slate-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"px-4 py-3 border-b border-slate-200 dark:border-slate-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                                        children: session?.user?.name || 'User'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                        lineNumber: 109,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400 truncate\",\n                                                                        children: session?.user?.email || '<EMAIL>'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                        lineNumber: 112,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                href: \"/profile\",\n                                                                className: \"block px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 w-full text-left\",\n                                                                onClick: ()=>setUserMenuOpen(false),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                            lineNumber: 122,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Your Profile\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.signOut)({\n                                                                        callbackUrl: '/login'\n                                                                    }),\n                                                                className: \"block px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 w-full text-left\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                            lineNumber: 131,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Sign out\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `fixed inset-0 z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-slate-600 bg-opacity-75\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-y-0 left-0 flex max-w-xs w-full bg-white dark:bg-slate-800\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center h-16 px-4 border-b border-slate-200 dark:border-slate-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"h-8 w-8 rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center mr-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold text-xs\",\n                                                                children: \"TM\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                            children: \"TaskMaster\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                                    onClick: ()=>setSidebarOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"Close sidebar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_Bars3Icon_BuildingOfficeIcon_ChartBarIcon_ClipboardDocumentListIcon_Cog6ToothIcon_HomeIcon_UserCircleIcon_UserGroupIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-6 w-6 text-slate-500\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"flex-1 overflow-y-auto px-2 py-4 space-y-1\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: item.href,\n                                                    className: `group flex items-center px-3 py-2 text-sm font-medium rounded-md ${isActive(item.href) ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300' : 'text-slate-600 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-700'}`,\n                                                    onClick: ()=>setSidebarOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            className: `mr-3 h-5 w-5 flex-shrink-0 ${isActive(item.href) ? 'text-blue-700 dark:text-blue-300' : 'text-slate-500 dark:text-slate-400'}`,\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1 relative overflow-y-auto focus:outline-none p-4 sm:p-6 lg:p-8\",\n                                    children: [\n                                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-semibold text-slate-900 dark:text-white\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        children\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\AppLayout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NotificationCenter.tsx":
/*!***********************************************!*\
  !*** ./src/components/NotificationCenter.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BellIcon,CheckCircleIcon,CheckIcon,ExclamationCircleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellAlertIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BellAlertIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/BellAlertIcon.js\");\n/* harmony import */ var _lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/notification-utils */ \"(ssr)/./src/lib/notification-utils.ts\");\n\n\n\n\n\n\nfunction NotificationCenter() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationCenter.useEffect\": ()=>{\n            // Initial load\n            const userId = session?.user?.id;\n            const allNotifications = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n            setNotifications(allNotifications);\n            setUnreadCount((0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.countUnread)(userId));\n            // Listen for new notifications\n            const handleNewNotification = {\n                \"NotificationCenter.useEffect.handleNewNotification\": (event)=>{\n                    const userNotifications = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n                    setNotifications(userNotifications);\n                    setUnreadCount((0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.countUnread)(userId));\n                }\n            }[\"NotificationCenter.useEffect.handleNewNotification\"];\n            // Listen for notifications from other tabs/windows\n            const handleStorageChange = {\n                \"NotificationCenter.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'lastNotification') {\n                        const userNotifications = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n                        setNotifications(userNotifications);\n                        setUnreadCount((0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.countUnread)(userId));\n                    }\n                }\n            }[\"NotificationCenter.useEffect.handleStorageChange\"];\n            // Add event listeners\n            window.addEventListener('new-notification', handleNewNotification);\n            window.addEventListener('storage', handleStorageChange);\n            // Check for outside clicks to close dropdown\n            const handleClickOutside = {\n                \"NotificationCenter.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"NotificationCenter.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            // Clean up\n            return ({\n                \"NotificationCenter.useEffect\": ()=>{\n                    window.removeEventListener('new-notification', handleNewNotification);\n                    window.removeEventListener('storage', handleStorageChange);\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"NotificationCenter.useEffect\"];\n        }\n    }[\"NotificationCenter.useEffect\"], [\n        session\n    ]);\n    const handleMarkAsRead = (id)=>{\n        (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.markAsRead)(id);\n        const userId = session?.user?.id;\n        const updatedNotifications = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n        setNotifications(updatedNotifications);\n        setUnreadCount((0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.countUnread)(userId));\n    };\n    const handleMarkAllAsRead = ()=>{\n        const userId = session?.user?.id;\n        (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.markAllAsRead)(userId);\n        const updatedNotifications = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n        setNotifications(updatedNotifications);\n        setUnreadCount(0);\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-amber-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n            case 'info':\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const formatTimestamp = (date)=>{\n        const now = new Date();\n        const diff = now.getTime() - new Date(date).getTime();\n        // Less than a minute\n        if (diff < 60000) {\n            return 'Just now';\n        }\n        // Less than an hour\n        if (diff < 3600000) {\n            const minutes = Math.floor(diff / 60000);\n            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\n        }\n        // Less than a day\n        if (diff < 86400000) {\n            const hours = Math.floor(diff / 3600000);\n            return `${hours} hour${hours > 1 ? 's' : ''} ago`;\n        }\n        // Less than a week\n        if (diff < 604800000) {\n            const days = Math.floor(diff / 86400000);\n            return `${days} day${days > 1 ? 's' : ''} ago`;\n        }\n        // Format as date\n        return new Date(date).toLocaleDateString(undefined, {\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"relative p-2 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700 focus:outline-none transition-colors\",\n                \"aria-label\": \"Notifications\",\n                children: unreadCount > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellAlertIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"absolute top-0 right-0.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white\",\n                            children: unreadCount > 9 ? '9+' : unreadCount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 sm:w-96 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-medium text-slate-900 dark:text-white\",\n                                children: \"Notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleMarkAllAsRead,\n                                        className: \"p-1 rounded text-xs text-slate-600 hover:text-slate-900 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-700 flex items-center space-x-1\",\n                                        title: \"Mark all as read\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-3.5 w-3.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Mark all read\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"p-1 rounded text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-700\",\n                                        title: \"Close\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto\",\n                        children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-slate-200 dark:divide-slate-700\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 hover:bg-slate-50 dark:hover:bg-slate-700/50 flex items-start space-x-3 transition-colors ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/10' : ''}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 mt-0.5\",\n                                            children: getNotificationIcon(notification.type)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: `text-sm ${!notification.read ? 'font-medium text-slate-900 dark:text-white' : 'text-slate-700 dark:text-slate-300'}`,\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-500 dark:text-slate-400 mt-1\",\n                                                    children: formatTimestamp(notification.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 23\n                                                }, this),\n                                                notification.projectTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs font-medium text-blue-600 dark:text-blue-400 mt-1\",\n                                                    children: notification.projectTitle\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 21\n                                        }, this),\n                                        !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleMarkAsRead(notification.id),\n                                            className: \"flex-shrink-0 p-1 rounded-full text-slate-400 hover:text-slate-700 hover:bg-slate-200 dark:text-slate-500 dark:hover:text-slate-300 dark:hover:bg-slate-700\",\n                                            title: \"Mark as read\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-3.5 w-3.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, notification.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center py-8 px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-full bg-slate-100 dark:bg-slate-700 p-3 mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6 text-slate-400 dark:text-slate-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-slate-500 dark:text-slate-500 mt-1\",\n                                    children: \"Notifications about your projects will appear here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-200 dark:border-slate-700 p-3 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const userId = session?.user?.id;\n                                const refreshed = (0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.getNotifications)(userId);\n                                setNotifications(refreshed);\n                                setUnreadCount((0,_lib_notification_utils__WEBPACK_IMPORTED_MODULE_3__.countUnread)(userId));\n                            },\n                            className: \"text-xs text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white flex items-center space-x-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_BellIcon_CheckCircleIcon_CheckIcon_ExclamationCircleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\NotificationCenter.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NotificationCenter.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BuildingOffice2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowRightOnRectangleIcon,BuildingOffice2Icon,ChartBarIcon,ChevronDownIcon,ChevronLeftIcon,ClipboardDocumentListIcon,Cog6ToothIcon,DocumentTextIcon,HomeIcon,PlusIcon,UserGroupIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst mainNavItems = [\n    {\n        name: 'Dashboard',\n        href: '/',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: 'Projects',\n        href: '/projects',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: 'Bulk Import',\n        href: '/projects/bulk-import',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: 'Team',\n        href: '/team',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: 'Departments',\n        href: '/departments',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: 'Analytics',\n        href: '/analytics',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: 'Activity Logs',\n        href: '/logs',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nconst settingsNavItems = [\n    {\n        name: 'Settings',\n        href: '/settings',\n        icon: _barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction Sidebar({ className = '' }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [userMenuOpen, setUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const handleLogout = async ()=>{\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n                callbackUrl: '/auth/signin'\n            });\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setUserMenuOpen(false);\n                    }\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"Sidebar.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"Sidebar.useEffect\"];\n        }\n    }[\"Sidebar.useEffect\"], []);\n    const getUserInitials = ()=>{\n        if (session?.user?.name) {\n            return session.user.name.split(' ').map((part)=>part[0]).join('').toUpperCase().slice(0, 2);\n        }\n        return session?.user?.email?.[0]?.toUpperCase() || 'U';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        flex h-screen flex-col border-r border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 \n        sticky top-0 left-0 min-h-screen transition-all duration-300 ease-in-out ${className}\n        ${collapsed ? 'w-20' : 'w-64'}\n      `,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 flex items-center px-4 justify-between border-b border-slate-200 dark:border-slate-800\",\n                children: [\n                    !collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setCollapsed(!collapsed),\n                        className: \"absolute -right-3 top-16 z-10 h-6 w-6 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-md border-2 border-white dark:border-slate-800 hover:bg-blue-600 transition-colors\",\n                        \"aria-label\": collapsed ? 'Expand sidebar' : 'Collapse sidebar',\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: `h-4 w-4 transition-transform duration-300 ${collapsed ? 'rotate-180' : ''}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/projects/new\",\n                    className: `\n            w-full btn btn-primary rounded-lg bg-blue-500 hover:bg-blue-600 text-white shadow-sm transition-all\n            flex items-center justify-center py-2.5\n            ${collapsed ? 'px-2' : 'px-4'}\n          `,\n                    title: \"New Project\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2\",\n                            children: \"New Project\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 26\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: `flex-1 ${collapsed ? 'px-2' : 'px-3'}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1\",\n                            children: mainNavItems.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: `\n                      group flex items-center ${collapsed ? 'justify-center' : 'justify-start'} \n                      gap-x-3 rounded-md px-3 py-2 text-sm font-medium\n                      ${isActive ? \"bg-slate-100 text-slate-900 dark:bg-slate-700 dark:text-white\" : \"text-slate-700 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-700/60 dark:hover:text-white\"}\n                      transition-all\n                    `,\n                                        title: collapsed ? item.name : '',\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                className: `\n                      h-5 w-5 flex-shrink-0\n                      ${isActive ? \"text-blue-600 dark:text-blue-400\" : \"text-slate-500 group-hover:text-blue-600 dark:text-slate-400 dark:group-hover:text-blue-400\"}\n                    `\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 21\n                                            }, this),\n                                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 36\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `my-6 ${collapsed ? 'px-2' : 'px-4'}`,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-[1px] w-full rounded-full bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200 dark:from-slate-700 dark:via-slate-600 dark:to-slate-700 opacity-70\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"px-3 mb-2 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1\",\n                                    children: settingsNavItems.map((item)=>{\n                                        const isActive = pathname === item.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: item.href,\n                                                className: `\n                        group flex items-center ${collapsed ? 'justify-center' : 'justify-start'} \n                        gap-x-3 rounded-md px-3 py-2 text-sm font-medium\n                        ${isActive ? \"bg-slate-100 text-slate-900 dark:bg-slate-700 dark:text-white\" : \"text-slate-700 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-700/60 dark:hover:text-white\"}\n                      `,\n                                                title: collapsed ? item.name : '',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: `\n                        h-5 w-5 flex-shrink-0\n                        ${isActive ? \"text-blue-600 dark:text-blue-400\" : \"text-slate-500 group-hover:text-blue-600 dark:text-slate-400 dark:group-hover:text-blue-400\"}\n                      `\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-slate-200 dark:border-slate-800 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setUserMenuOpen(!userMenuOpen),\n                            className: `w-full flex items-center ${collapsed ? 'justify-center' : 'justify-between'} p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center ${collapsed ? 'justify-center' : ''}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center text-white text-sm font-medium\",\n                                            children: getUserInitials()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-slate-900 dark:text-white truncate\",\n                                                    children: session?.user?.name || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-slate-500 dark:text-slate-400 truncate\",\n                                                    children: session?.user?.role || 'Member'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: `h-4 w-4 text-slate-400 transition-transform ${userMenuOpen ? 'rotate-180' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        userMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `absolute ${collapsed ? 'bottom-full -left-32' : 'bottom-full left-0'} w-48 mb-2 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg z-50`,\n                            ref: dropdownRef,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/settings\",\n                                        className: \"flex items-center px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700\",\n                                        onClick: ()=>setUserMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Profile Settings\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowRightOnRectangleIcon_BuildingOffice2Icon_ChartBarIcon_ChevronDownIcon_ChevronLeftIcon_ClipboardDocumentListIcon_Cog6ToothIcon_DocumentTextIcon_HomeIcon_PlusIcon_UserGroupIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SunIcon.js\");\n/* harmony import */ var _barrel_optimize_names_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MoonIcon,SunIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n\n\n\nfunction ThemeToggle() {\n    const [isDark, setIsDark] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme state based on localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeToggle.useEffect\": ()=>{\n            // Check for saved theme preference; default to light\n            const savedTheme = localStorage.getItem('theme');\n            const initialIsDark = savedTheme === 'dark';\n            setIsDark(initialIsDark);\n            document.documentElement.classList.toggle('dark', initialIsDark);\n        }\n    }[\"ThemeToggle.useEffect\"], []);\n    const toggleTheme = ()=>{\n        const newTheme = !isDark;\n        setIsDark(newTheme);\n        document.documentElement.classList.toggle('dark', newTheme);\n        localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"p-2 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700 focus:outline-none transition-colors\",\n        \"aria-label\": \"Toggle theme\",\n        children: isDark ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 30,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoonIcon_SunIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 32,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/notification-utils.ts":
/*!***************************************!*\
  !*** ./src/lib/notification-utils.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   countUnread: () => (/* binding */ countUnread),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   markAllAsRead: () => (/* binding */ markAllAsRead),\n/* harmony export */   markAsRead: () => (/* binding */ markAsRead),\n/* harmony export */   notifyError: () => (/* binding */ notifyError),\n/* harmony export */   notifyPriorityChange: () => (/* binding */ notifyPriorityChange),\n/* harmony export */   notifyProjectUpdate: () => (/* binding */ notifyProjectUpdate),\n/* harmony export */   notifyStatusChange: () => (/* binding */ notifyStatusChange)\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n// Keep track of notifications in memory\nlet notifications = [];\nconst MAX_NOTIFICATIONS = 50;\n// Add a new notification\nconst addNotification = (notification)=>{\n    const newNotification = {\n        id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        timestamp: new Date(),\n        read: false,\n        ...notification\n    };\n    // Add to the beginning of the array\n    notifications = [\n        newNotification,\n        ...notifications\n    ].slice(0, MAX_NOTIFICATIONS);\n    // Broadcast the notification to all tabs/windows\n    try {\n        localStorage.setItem('lastNotification', JSON.stringify(newNotification));\n        const event = new CustomEvent('new-notification', {\n            detail: newNotification\n        });\n        window.dispatchEvent(event);\n    } catch (e) {\n        console.error('Failed to broadcast notification:', e);\n    }\n    return newNotification;\n};\n// Get all notifications\nconst getNotifications = (userId)=>{\n    if (userId) {\n        return notifications.filter((n)=>!n.userId || n.userId === userId);\n    }\n    return notifications;\n};\n// Mark a notification as read\nconst markAsRead = (id)=>{\n    notifications = notifications.map((n)=>n.id === id ? {\n            ...n,\n            read: true\n        } : n);\n    return notifications;\n};\n// Mark all notifications as read\nconst markAllAsRead = (userId)=>{\n    if (userId) {\n        notifications = notifications.map((n)=>!n.userId || n.userId === userId ? {\n                ...n,\n                read: true\n            } : n);\n    } else {\n        notifications = notifications.map((n)=>({\n                ...n,\n                read: true\n            }));\n    }\n    return notifications;\n};\n// Count unread notifications\nconst countUnread = (userId)=>{\n    if (userId) {\n        return notifications.filter((n)=>!n.read && (!n.userId || n.userId === userId)).length;\n    }\n    return notifications.filter((n)=>!n.read).length;\n};\n// Helper functions for different notification types\nconst notifyProjectUpdate = (projectTitle, action, userId, projectId)=>{\n    const notification = addNotification({\n        message: `Project \"${projectTitle}\" ${action}`,\n        type: 'info',\n        userId,\n        projectId,\n        projectTitle,\n        action\n    });\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.success(`Project \"${projectTitle}\" ${action}`, {\n        position: 'bottom-right',\n        duration: 3000\n    });\n    return notification;\n};\nconst notifyStatusChange = (projectTitle, fromStatus, toStatus, userId, projectId)=>{\n    const notification = addNotification({\n        message: `Project \"${projectTitle}\" moved from ${fromStatus} to ${toStatus}`,\n        type: 'success',\n        userId,\n        projectId,\n        projectTitle,\n        action: 'status-change'\n    });\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.success(`Status changed: ${fromStatus} → ${toStatus}`, {\n        position: 'bottom-right',\n        duration: 4000\n    });\n    return notification;\n};\nconst notifyPriorityChange = (projectTitle, userId, projectId)=>{\n    const notification = addNotification({\n        message: `Priority updated for project \"${projectTitle}\"`,\n        type: 'info',\n        userId,\n        projectId,\n        projectTitle,\n        action: 'priority-change'\n    });\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.success(`Priority updated for \"${projectTitle}\"`, {\n        position: 'bottom-right',\n        duration: 3000\n    });\n    return notification;\n};\nconst notifyError = (message, userId)=>{\n    const notification = addNotification({\n        message,\n        type: 'error',\n        userId\n    });\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message, {\n        position: 'bottom-right',\n        duration: 5000\n    });\n    return notification;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/notification-utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/@heroicons","vendor-chunks/react-hot-toast","vendor-chunks/goober"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();