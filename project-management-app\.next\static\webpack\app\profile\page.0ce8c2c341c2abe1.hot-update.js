"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AppLayout */ \"(app-pages-browser)/./src/components/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon,KeyIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProfilePage() {\n    var _session_user;\n    _s();\n    const { data: session, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [department, setDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        text: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfilePage.useEffect\": ()=>{\n            const loadUserProfile = {\n                \"ProfilePage.useEffect.loadUserProfile\": async ()=>{\n                    var _session_user;\n                    if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                        try {\n                            const response = await fetch('/api/user/profile');\n                            if (response.ok) {\n                                const userData = await response.json();\n                                setName(userData.name || '');\n                                setEmail(userData.email || '');\n                                setRole(userData.role || 'User');\n                                setDepartment(userData.department || '');\n                            } else {\n                                // Fallback to session data if API fails\n                                setName(session.user.name || '');\n                                setEmail(session.user.email || '');\n                                setRole(session.user.role || 'User');\n                                setDepartment(session.user.department || '');\n                            }\n                        } catch (error) {\n                            console.error('Error loading user profile:', error);\n                            // Fallback to session data\n                            setName(session.user.name || '');\n                            setEmail(session.user.email || '');\n                            setRole(session.user.role || 'User');\n                            setDepartment(session.user.department || '');\n                        }\n                    }\n                }\n            }[\"ProfilePage.useEffect.loadUserProfile\"];\n            loadUserProfile();\n        }\n    }[\"ProfilePage.useEffect\"], [\n        session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSaving(true);\n        setMessage({\n            type: '',\n            text: ''\n        });\n        try {\n            // Validate required fields\n            if (!name.trim()) {\n                setMessage({\n                    type: 'error',\n                    text: 'Name is required'\n                });\n                return;\n            }\n            if (!email.trim() || !email.includes('@')) {\n                setMessage({\n                    type: 'error',\n                    text: 'Valid email is required'\n                });\n                return;\n            }\n            // Update user profile via API\n            const response = await fetch('/api/user/profile', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: name.trim(),\n                    email: email.trim(),\n                    department: department.trim() || null\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to update profile');\n            }\n            // Update the session with new data\n            await update({\n                ...session,\n                user: {\n                    ...session === null || session === void 0 ? void 0 : session.user,\n                    name: data.user.name,\n                    email: data.user.email\n                }\n            });\n            setIsEditing(false);\n            setMessage({\n                type: 'success',\n                text: 'Profile updated successfully!'\n            });\n            // Refresh the page to reflect changes\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1500);\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            setMessage({\n                type: 'error',\n                text: error instanceof Error ? error.message : 'Failed to update profile. Please try again.'\n            });\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        title: \"Your Profile\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-3xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-slate-800 shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-8 border-b border-slate-200 dark:border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-20 w-20 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center text-2xl font-bold text-white\",\n                                    children: name.charAt(0).toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                                            children: name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-400\",\n                                            children: role\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this),\n                                        department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 dark:text-slate-400\",\n                                            children: [\n                                                \"Department: \",\n                                                department\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 rounded-lg \".concat(message.type === 'success' ? 'bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-50 text-red-800 dark:bg-red-900/30 dark:text-red-300'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        message.type === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-5 w-5 mr-2 text-red-500\",\n                                            children: \"!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this),\n                                        message.text\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"name\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Full Name\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"name\",\n                                                        name: \"name\",\n                                                        value: name,\n                                                        onChange: (e)=>setName(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"email\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Email\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"email\",\n                                                        name: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"role\",\n                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_KeyIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-slate-500 dark:text-slate-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Role\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"role\",\n                                                        name: \"role\",\n                                                        value: role,\n                                                        onChange: (e)=>setRole(e.target.value),\n                                                        disabled: !isEditing,\n                                                        className: \"mt-1 block w-full px-4 py-3 border border-slate-200 dark:border-slate-600 dark:bg-slate-700 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-70 disabled:cursor-not-allowed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"User\",\n                                                                children: \"User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Admin\",\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PMO\",\n                                                                children: \"PMO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex justify-end\",\n                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setIsEditing(false),\n                                                    className: \"px-4 py-2 border border-slate-300 text-slate-700 dark:text-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 mr-3\",\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSaving,\n                                                    className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-70 disabled:cursor-not-allowed\",\n                                                    children: isSaving ? 'Saving...' : 'Save Changes'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setIsEditing(true),\n                                            className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                                            children: \"Edit Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"+M8igg2e6XvUZzcSfzfmt1B7fcQ=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/profile/page.tsx\n"));

/***/ })

});