/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/profile/route";
exports.ids = ["app/api/user/profile/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user/profile/route.ts */ \"(rsc)/./src/app/api/user/profile/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/profile/route\",\n        pathname: \"/api/user/profile\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/profile/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\api\\\\user\\\\profile\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_user_profile_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ1c2VyJTJGcHJvZmlsZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGdXNlciUyRnByb2ZpbGUlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZ1c2VyJTJGcHJvZmlsZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNtb2hkOSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUNwcm9qZWN0LW1hbmFnZW1lbnQtYXBwJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNtb2hkOSU1Q0Rvd25sb2FkcyU1Q3Byb2plY3QtbWFuYWdlbWVudC1hcHAlNUNwcm9qZWN0LW1hbmFnZW1lbnQtYXBwJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNtRTtBQUNoSjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcbW9oZDlcXFxcRG93bmxvYWRzXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFx1c2VyXFxcXHByb2ZpbGVcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3VzZXIvcHJvZmlsZS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3VzZXIvcHJvZmlsZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvdXNlci9wcm9maWxlL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcbW9oZDlcXFxcRG93bmxvYWRzXFxcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFx1c2VyXFxcXHByb2ZpbGVcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user/profile/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/user/profile/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/userService */ \"(rsc)/./src/services/userService.ts\");\n\n\n\n\n// GET /api/user/profile - Get current user's profile\nasync function GET() {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const user = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_3__.getUserById)(session.user.id);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        // Remove password from response\n        const { password, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(userWithoutPassword);\n    } catch (error) {\n        console.error('Error fetching user profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch profile'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/user/profile - Update current user's profile\nasync function PUT(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { name, email, department, departmentId, phone, bio, jobTitle } = body;\n        // Validate required fields\n        if (!name || !name.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!email || !email.trim() || !email.includes('@')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid email is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if user exists\n        const existingUser = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_3__.getUserById)(session.user.id);\n        if (!existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        // Prepare update data\n        const updateData = {\n            name: name.trim(),\n            email: email.trim()\n        };\n        // Add optional fields if provided\n        if (department !== undefined) updateData.department = department;\n        if (departmentId !== undefined) updateData.departmentId = departmentId;\n        if (phone !== undefined) updateData.phone = phone;\n        if (bio !== undefined) updateData.bio = bio;\n        if (jobTitle !== undefined) updateData.jobTitle = jobTitle;\n        // Update user in Firestore\n        const updatedUser = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_3__.updateUser)(session.user.id, updateData);\n        // Remove password from response\n        const { password, ...userWithoutPassword } = updatedUser;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Profile updated successfully',\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error('Error updating user profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update profile',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// PATCH /api/user/profile - Partially update current user's profile\nasync function PATCH(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Not authenticated'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        // Check if user exists\n        const existingUser = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_3__.getUserById)(session.user.id);\n        if (!existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        // Validate email if provided\n        if (body.email !== undefined && (!body.email.trim() || !body.email.includes('@'))) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid email is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate name if provided\n        if (body.name !== undefined && !body.name.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name cannot be empty'\n            }, {\n                status: 400\n            });\n        }\n        // Prepare update data (only include fields that are provided)\n        const updateData = {};\n        if (body.name !== undefined) updateData.name = body.name.trim();\n        if (body.email !== undefined) updateData.email = body.email.trim();\n        if (body.department !== undefined) updateData.department = body.department;\n        if (body.departmentId !== undefined) updateData.departmentId = body.departmentId;\n        if (body.phone !== undefined) updateData.phone = body.phone;\n        if (body.bio !== undefined) updateData.bio = body.bio;\n        if (body.jobTitle !== undefined) updateData.jobTitle = body.jobTitle;\n        // Only update if there are changes\n        if (Object.keys(updateData).length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'No changes to update'\n            });\n        }\n        // Update user in Firestore\n        const updatedUser = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_3__.updateUser)(session.user.id, updateData);\n        // Remove password from response\n        const { password, ...userWithoutPassword } = updatedUser;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Profile updated successfully',\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error('Error updating user profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update profile',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user/profile/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n\n\n// Special case for development mode\nconst handleDevAuth = (email, password)=>{\n    if (true) {\n        // Default admin credentials\n        if (email === '<EMAIL>' && password === 'password') {\n            return {\n                id: 'admin',\n                name: 'Admin User',\n                email: '<EMAIL>',\n                role: 'ADMIN'\n            };\n        }\n        // Additional development user for AOPS\n        if (email === '<EMAIL>' && password === 'password') {\n            return {\n                id: 'aops-admin',\n                name: 'AOPS Admin',\n                email: '<EMAIL>',\n                role: 'ADMIN'\n            };\n        }\n    }\n    return null;\n};\nconst authOptions = {\n    secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here',\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'text'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                // First check for dev credentials\n                const devUser = handleDevAuth(credentials.email, credentials.password);\n                if (devUser) return devUser;\n                try {\n                    // Sign in with Firebase Auth\n                    const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password);\n                    const uid = userCredential.user.uid;\n                    // Get additional user data from Firestore\n                    const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid));\n                    if (!userDoc.exists()) {\n                        // Create a basic user record if it doesn't exist\n                        const userData = {\n                            name: credentials.email.split('@')[0],\n                            email: credentials.email,\n                            role: 'USER',\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        };\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), userData);\n                        return {\n                            id: uid,\n                            email: credentials.email,\n                            name: userData.name,\n                            role: userData.role\n                        };\n                    }\n                    const userData = userDoc.data();\n                    return {\n                        id: uid,\n                        email: credentials.email,\n                        name: userData.name || credentials.email.split('@')[0],\n                        role: userData.role || 'USER'\n                    };\n                } catch (error) {\n                    console.error('Firebase auth error:', error);\n                    // For development, create a fallback admin user\n                    if ( true && (credentials.email === '<EMAIL>' && credentials.password === 'password' || credentials.email === '<EMAIL>' && credentials.password === 'password')) {\n                        try {\n                            // Try to create the admin user in Firebase\n                            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, credentials.email, credentials.password).then(async (userCredential)=>{\n                                const uid = userCredential.user.uid;\n                                // Create admin user in Firestore\n                                const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';\n                                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_1__.db, 'users', uid), {\n                                    name: userName,\n                                    email: credentials.email,\n                                    role: 'ADMIN',\n                                    department: 'Admin',\n                                    createdAt: new Date().toISOString(),\n                                    updatedAt: new Date().toISOString()\n                                });\n                            }).catch((err)=>{\n                                console.log('Admin user may already exist:', err);\n                            });\n                            const userName = credentials.email === '<EMAIL>' ? 'AOPS Admin' : 'Admin User';\n                            const userId = credentials.email === '<EMAIL>' ? 'aops-admin' : 'admin';\n                            return {\n                                id: userId,\n                                name: userName,\n                                email: credentials.email,\n                                role: 'ADMIN'\n                            };\n                        } catch (createError) {\n                            console.error('Could not create admin user:', createError);\n                            return null;\n                        }\n                    }\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        jwt: async ({ token, user })=>{\n            if (user) {\n                token.role = user.role;\n                token.sub = user.id;\n            }\n            return token;\n        },\n        session: async ({ session, token })=>{\n            if (token && session.user) {\n                session.user.role = token.role;\n                session.user.id = token.sub;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        error: '/auth/error'\n    },\n    session: {\n        strategy: 'jwt',\n        maxAge: 30 * 24 * 60 * 60\n    },\n    debug: \"development\" === 'development'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n// Import the functions you need from the SDKs you need\n\n\n\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k\",\n    authDomain: \"project-management-f45cc.firebaseapp.com\",\n    projectId: \"project-management-f45cc\",\n    storageBucket: \"project-management-f45cc.firebasestorage.app\",\n    messagingSenderId: \"1002222709659\",\n    appId: \"1:1002222709659:web:6b1ab479efcc4102824f3e\",\n    measurementId: \"G-JYYNYZV8LP\"\n};\n// Initialize Firebase only if we don't already have an instance\n// This helps prevent multiple initializations during SSR/SSG\nlet app;\nif (!(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Analytics is now null by default\nconst analytics = null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUsersByDepartment: () => (/* binding */ getUsersByDepartment),\n/* harmony export */   getUsersByRole: () => (/* binding */ getUsersByRole),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Local implementation of toJsDate\nfunction toJsDate(date) {\n    if (!date) return null;\n    try {\n        // Already a Date object\n        if (date instanceof Date) {\n            return date;\n        }\n        // String date (ISO format or other string representation)\n        if (typeof date === 'string') {\n            // Handle ISO dates\n            if (date.match(/^\\d{4}-\\d{2}-\\d{2}/) || date.includes('T00:00:00')) {\n                const parsedDate = new Date(date);\n                if (!isNaN(parsedDate.getTime())) {\n                    return parsedDate;\n                }\n            }\n            // Try to parse as a date anyway\n            const parsedDate = new Date(date);\n            if (!isNaN(parsedDate.getTime())) {\n                return parsedDate;\n            }\n        }\n        // Numeric timestamp (milliseconds since epoch)\n        if (typeof date === 'number') {\n            return new Date(date);\n        }\n        // Firebase Timestamp with toDate() method\n        if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {\n            return date.toDate();\n        }\n        // Firebase Timestamp-like object with seconds and nanoseconds\n        if (date && typeof date === 'object' && 'seconds' in date) {\n            return new Date(date.seconds * 1000);\n        }\n        // Stringified object that might contain a timestamp\n        if (typeof date === 'string' && (date.includes('\"seconds\"') || date.includes('\"nanoseconds\"'))) {\n            try {\n                const parsed = JSON.parse(date);\n                if (parsed && typeof parsed === 'object' && 'seconds' in parsed) {\n                    return new Date(parsed.seconds * 1000);\n                }\n            } catch (e) {}\n        }\n    } catch (error) {\n        console.error('Error converting to JS Date:', error);\n    }\n    return null;\n}\n// Convert Firestore document to User object\nconst userConverter = {\n    fromFirestore (snapshot) {\n        const data = snapshot.data();\n        // Handle Firestore timestamps\n        let createdAt = new Date();\n        let updatedAt = new Date();\n        if (data.createdAt) {\n            createdAt = toJsDate(data.createdAt) || new Date();\n        }\n        if (data.updatedAt) {\n            updatedAt = toJsDate(data.updatedAt) || new Date();\n        }\n        return {\n            id: snapshot.id,\n            name: data.name || '',\n            email: data.email || '',\n            password: data.password || undefined,\n            role: data.role || 'USER',\n            department: data.department || null,\n            departmentId: data.departmentId || null,\n            createdAt: createdAt,\n            updatedAt: updatedAt,\n            phone: data.phone || undefined,\n            bio: data.bio || undefined,\n            jobTitle: data.jobTitle || undefined\n        };\n    },\n    toFirestore (user) {\n        const { password, ...userData } = user;\n        return {\n            ...userData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n    }\n};\n// Collection reference\nconst usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users');\n// Get all users\nasync function getAllUsers() {\n    try {\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(usersCollection);\n        const users = snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n        // Fetch department names for each user\n        const usersWithDepartments = await Promise.all(users.map(async (user)=>{\n            if (user.departmentId) {\n                try {\n                    const deptDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments', user.departmentId));\n                    if (deptDoc.exists()) {\n                        const deptData = deptDoc.data();\n                        return {\n                            ...user,\n                            departmentName: deptData.name || null\n                        };\n                    }\n                } catch (error) {\n                    console.error(`Error fetching department for user ${user.id}:`, error);\n                }\n            }\n            return user;\n        }));\n        return usersWithDepartments;\n    } catch (error) {\n        console.error('Error getting users:', error);\n        throw error;\n    }\n}\n// Get user by ID\nasync function getUserById(id) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id);\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!snapshot.exists()) {\n            return null;\n        }\n        return userConverter.fromFirestore(snapshot);\n    } catch (error) {\n        console.error(`Error getting user ${id}:`, error);\n        throw error;\n    }\n}\n// Get user by email\nasync function getUserByEmail(email) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('email', '==', email));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (snapshot.empty) {\n            return null;\n        }\n        return userConverter.fromFirestore(snapshot.docs[0]);\n    } catch (error) {\n        console.error(`Error getting user by email ${email}:`, error);\n        throw error;\n    }\n}\n// Create new user\nasync function createUser(userData) {\n    try {\n        const data = {\n            ...userData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(usersCollection, data);\n        const newDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        return userConverter.fromFirestore(newDoc);\n    } catch (error) {\n        console.error('Error creating user:', error);\n        throw error;\n    }\n}\n// Update user\nasync function updateUser(id, userData) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id);\n        // Add updatedAt timestamp\n        const data = {\n            ...userData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(docRef, data);\n        // Get updated document\n        const updatedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!updatedDoc.exists()) {\n            throw new Error(`User with ID ${id} not found`);\n        }\n        return userConverter.fromFirestore(updatedDoc);\n    } catch (error) {\n        console.error(`Error updating user ${id}:`, error);\n        throw error;\n    }\n}\n// Delete user\nasync function deleteUser(id) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id));\n    } catch (error) {\n        console.error(`Error deleting user ${id}:`, error);\n        throw error;\n    }\n}\nasync function getUsersByDepartment(departmentId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', departmentId));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting users by department ${departmentId}:`, error);\n        throw error;\n    }\n}\nasync function getUsersByRole(role) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('role', '==', role));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting users by role ${role}:`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/userService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fprofile%2Froute&page=%2Fapi%2Fuser%2Fprofile%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fprofile%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();