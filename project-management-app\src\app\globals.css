@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme color palette */
    --primary: 59 130 246; /* Blue-500 */
    --primary-foreground: 255 255 255;
    --secondary: 241 245 249; /* Slate-100 */
    --secondary-foreground: 51 65 85; /* Slate-700 */
    --accent: 139 92 246; /* Violet-500 */
    --accent-foreground: 255 255 255;
    
    /* Semantic colors */
    --success: 34 197 94; /* Green-500 */
    --warning: 245 158 11; /* Amber-500 */
    --danger: 239 68 68; /* Red-500 */
    --info: 14 165 233; /* Sky-500 */
    
    /* Background colors */
    --background: 255 255 255; /* White */
    --background-secondary: 248 250 252; /* Slate-50 */
    --background-tertiary: 241 245 249; /* Slate-100 */
    
    /* Text colors */
    --foreground: 15 23 42; /* Slate-900 */
    --foreground-secondary: 71 85 105; /* Slate-600 */
    --foreground-muted: 148 163 184; /* Slate-400 */
    
    /* Border and input colors */
    --border: 226 232 240; /* Slate-200 */
    --border-light: 241 245 249; /* Slate-100 */
    --input: 255 255 255;
    --input-border: 203 213 225; /* Slate-300 */
    
    /* Card and surface colors */
    --card: 255 255 255;
    --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --card-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    
    --radius: 0.75rem;
  }
  
  .dark {
    /* Dark theme color palette */
    --primary: 59 130 246; /* Blue-500 */
    --primary-foreground: 255 255 255;
    --secondary: 51 65 85; /* Slate-700 */
    --secondary-foreground: 241 245 249; /* Slate-100 */
    --accent: 139 92 246; /* Violet-500 */
    --accent-foreground: 255 255 255;
    
    /* Semantic colors - darkened */
    --success: 34 197 94; /* Green-500 */
    --warning: 245 158 11; /* Amber-500 */
    --danger: 239 68 68; /* Red-500 */
    --info: 14 165 233; /* Sky-500 */
    
    /* Background colors */
    --background: 15 23 42; /* Slate-900 */
    --background-secondary: 30 41 59; /* Slate-800 */
    --background-tertiary: 51 65 85; /* Slate-700 */
    
    /* Text colors */
    --foreground: 248 250 252; /* Slate-50 */
    --foreground-secondary: 226 232 240; /* Slate-200 */
    --foreground-muted: 148 163 184; /* Slate-400 */
    
    /* Border and input colors */
    --border: 51 65 85; /* Slate-700 */
    --border-light: 71 85 105; /* Slate-600 */
    --input: 30 41 59; /* Slate-800 */
    --input-border: 71 85 105; /* Slate-600 */
    
    /* Card and surface colors */
    --card: 30 41 59; /* Slate-800 */
    --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --card-shadow-hover: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  }
  
  /* High contrast mode */
  .high-contrast {
    --primary: 37 99 235; /* Blue-600 */
    --secondary: 30 64 175; /* Blue-800 */
    --foreground: 255 255 255; /* White */
    --foreground-secondary: 241 245 249; /* Slate-100 */
    --border: 255 255 255; /* White */
    
    /* Increased text contrast */
    &.dark {
      --foreground: 255 255 255; /* Pure white */
      --foreground-secondary: 241 245 249; /* Slate-100 */
      --foreground-muted: 226 232 240; /* Slate-200 */
      --background: 2 6 23; /* Darker than slate-900 */
      --background-secondary: 15 23 42; /* Slate-900 */
      --border: 255 255 255; /* White */
    }
  }
  
  /* Compact mode - reduce spacing */
  .compact {
    --spacing-sm: 0.5rem; /* 8px */
    --spacing-md: 0.75rem; /* 12px */
    --spacing-lg: 1rem; /* 16px */
  }
  
  /* Regular spacing (default) */
  :root {
    --spacing-sm: 0.75rem; /* 12px */
    --spacing-md: 1rem; /* 16px */
    --spacing-lg: 1.5rem; /* 24px */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-slate-50 text-slate-900 font-sans antialiased dark:bg-slate-900 dark:text-slate-100;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply text-slate-900 font-semibold dark:text-white;
  }
}

/* Custom component styles */
@layer components {
  .card {
    @apply bg-white rounded-xl border border-slate-200 shadow-sm dark:bg-slate-800 dark:border-slate-700;
  }
  
  .card-hover {
    @apply card hover:shadow-md transition-shadow duration-300;
  }
  
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-slate-900;
  }
  
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500 shadow-sm;
  }
  
  .btn-secondary {
    @apply bg-slate-100 text-slate-700 hover:bg-slate-200 focus:ring-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:focus:ring-slate-400;
  }
  
  .btn-ghost {
    @apply text-slate-600 hover:text-slate-900 hover:bg-slate-100 focus:ring-slate-500 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-700;
  }
  
  .input {
    @apply w-full px-3 py-2 text-sm border border-slate-300 rounded-lg bg-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-800 dark:border-slate-700 dark:text-white dark:placeholder-slate-500;
  }
  
  .sidebar-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .sidebar-item-active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-500 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-400;
  }
  
  .sidebar-item-inactive {
    @apply text-slate-600 hover:text-slate-900 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-200 dark:hover:bg-slate-800;
  }
  
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-working {
    @apply bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300;
  }
  
  .status-done {
    @apply bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300;
  }
  
  .status-stuck {
    @apply bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300;
  }
  
  .priority-high {
    @apply bg-red-50 text-red-700 dark:bg-red-900/40 dark:text-red-300;
  }
  
  .priority-medium {
    @apply bg-amber-50 text-amber-700 dark:bg-amber-900/40 dark:text-amber-300;
  }
  
  .priority-low {
    @apply bg-blue-50 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-violet-600 bg-clip-text text-transparent;
  }
  
  .avatar {
    @apply inline-flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-violet-500 text-white font-medium;
  }
  
  /* Compact mode adjustments */
  .compact .card {
    @apply p-2;
  }
  
  .compact .btn {
    @apply px-3 py-1.5 text-xs;
  }
  
  .compact .input {
    @apply px-2 py-1.5 text-xs;
  }
  
  .compact .sidebar-item {
    @apply px-2 py-1.5 text-xs;
  }
  
  /* High contrast mode adjustments */
  .high-contrast .btn-primary {
    @apply bg-blue-700 text-white border-2 border-white;
  }
  
  .high-contrast.dark .card {
    @apply border-2 border-slate-400;
  }
  
  .high-contrast.dark .input {
    @apply border-2 border-slate-400;
  }
}

/* Add custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #CBD5E1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

::-webkit-scrollbar-thumb:hover {
  background: #94A3B8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748B;
}

/* Add text selection styling */
::selection {
  background-color: rgba(59, 130, 246, 0.3);
  color: inherit;
}

.dark ::selection {
  background-color: rgba(96, 165, 250, 0.4);
  color: inherit;
}

/* Improve form control focus states in dark mode */
.dark input:focus,
.dark select:focus,
.dark textarea:focus {
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.4);
}

/* Ensure date inputs have proper styling in dark mode */
.dark input[type="date"] {
  color-scheme: dark;
}

/* Enhance form controls in dark mode */
.dark input,
.dark select,
.dark textarea {
  border-color: rgb(71, 85, 105); /* slate-600 */
  background-color: rgb(51, 65, 85); /* slate-700 */
  color: rgb(241, 245, 249); /* slate-100 */
}

.dark input::placeholder,
.dark textarea::placeholder {
  color: rgb(148, 163, 184); /* slate-400 */
}

/* Project modal specific improvements for dark mode */
.dark .form-modal {
  background-color: rgb(30, 41, 59); /* slate-800 */
  border-color: rgb(51, 65, 85); /* slate-700 */
}

.dark select option {
  background-color: rgb(30, 41, 59); /* slate-800 */
  color: rgb(241, 245, 249); /* slate-100 */
}

/* Improve readability of disabled inputs in dark mode */
.dark input:disabled,
.dark select:disabled,
.dark textarea:disabled {
  background-color: rgb(71, 85, 105); /* slate-600 */
  color: rgb(203, 213, 225); /* slate-300 */
  opacity: 0.8;
}

/* Enhance form field hover states */
.dark input:hover:not(:disabled):not(:focus),
.dark select:hover:not(:disabled):not(:focus),
.dark textarea:hover:not(:disabled):not(:focus) {
  border-color: rgb(94, 234, 212); /* teal-300 */
}

/* Improve error state visibility in dark mode */
.dark input.error,
.dark select.error,
.dark textarea.error {
  border-color: rgb(239, 68, 68); /* red-500 */
  background-color: rgba(239, 68, 68, 0.1);
}

/* Project form modal specific dark mode improvements */
.dark .fixed.inset-0 {
  background-color: rgba(0, 0, 0, 0.7);
}

.dark .bg-white.rounded-xl {
  background-color: rgb(30, 41, 59) !important; /* slate-800 */
  border-color: rgb(51, 65, 85) !important; /* slate-700 */
}

/* Override input styles for better dark mode contrast */
.dark input, 
.dark select, 
.dark textarea {
  color: white !important;
  background-color: rgb(51, 65, 85) !important; /* slate-700 */
  border-color: rgb(71, 85, 105) !important; /* slate-600 */
}

/* Fix select dropdowns in dark mode */
.dark select option {
  background-color: rgb(15, 23, 42) !important; /* slate-900 */
  color: white !important;
}

/* Improve font contrast for form labels in dark mode */
.dark label {
  color: rgb(226, 232, 240) !important; /* slate-200 */
}

/* Fix select arrow color in dark mode */
.dark select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23f1f5f9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

/* Ensure placeholder text is visible */
.dark input::placeholder,
.dark textarea::placeholder {
  color: rgb(148, 163, 184) !important; /* slate-400 */
}

/* Fix date input appearance in dark mode */
.dark input[type="date"] {
  color-scheme: dark;
  color: white !important;
}

/* Dialog and modal specific dark mode improvements */
.dark .fixed.inset-0 > div[class*="bg-white"],
.dark .fixed.inset-0 > div[class*="rounded-xl"],
.dark .fixed.inset-0 .form-modal {
  background-color: rgb(30, 41, 59) !important; /* slate-800 */
  border-color: rgb(51, 65, 85) !important; /* slate-700 */
}

/* Dialog content backgrounds */
.dark .bg-slate-50 {
  background-color: rgb(51, 65, 85) !important; /* slate-700 */
}

/* Make modal headers more visible */
.dark h2, .dark h3 {
  color: white !important;
}

/* Fix input styling for dialogs */
.dark .fixed.inset-0 input, 
.dark .fixed.inset-0 select, 
.dark .fixed.inset-0 textarea {
  color: white !important;
  background-color: rgb(51, 65, 85) !important; /* slate-700 */
  border-color: rgb(71, 85, 105) !important; /* slate-600 */
}
