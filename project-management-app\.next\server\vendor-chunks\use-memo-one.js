"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-memo-one";
exports.ids = ["vendor-chunks/use-memo-one"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-memo-one/dist/use-memo-one.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/use-memo-one/dist/use-memo-one.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallback: () => (/* binding */ useCallback),\n/* harmony export */   useCallbackOne: () => (/* binding */ useCallbackOne),\n/* harmony export */   useMemo: () => (/* binding */ useMemo),\n/* harmony export */   useMemoOne: () => (/* binding */ useMemoOne)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n\n  for (var i = 0; i < newInputs.length; i++) {\n    if (newInputs[i] !== lastInputs[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction useMemoOne(getResult, inputs) {\n  var initial = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {\n    return {\n      inputs: inputs,\n      result: getResult()\n    };\n  })[0];\n  var isFirstRun = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  var committed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initial);\n  var useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  var cache = useCache ? committed.current : {\n    inputs: inputs,\n    result: getResult()\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallbackOne(callback, inputs) {\n  return useMemoOne(function () {\n    return callback;\n  }, inputs);\n}\nvar useMemo = useMemoOne;\nvar useCallback = useCallbackOne;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-memo-one/dist/use-memo-one.esm.js\n");

/***/ })

};
;