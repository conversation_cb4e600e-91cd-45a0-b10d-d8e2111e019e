# Project Management Application

A modern, full-stack project management web application built with Next.js, Tailwind CSS, and SQLite with Prisma ORM.

## Features

- Dashboard overview with project summaries and progress
- Create, update, delete, and list projects
- Project details including title, driver, type, year, focal person, CAPEX/OPEX, status, department, area, awarded company, and savings
- Task management for each project
- Modern, responsive UI inspired by Monday.com
- SQLite database for local development

## Tech Stack

- **Frontend**: Next.js, React, Tailwind CSS, Heroicons
- **Backend**: Next.js API Routes
- **Database**: SQLite with Prisma ORM
- **Authentication**: (To be implemented with NextAuth.js)

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd project-management-app
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Set up the database:

```bash
npx prisma migrate dev
```

4. Start the development server:

```bash
npm run dev
# or
yarn dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Project Structure

```
project-management-app/
├── prisma/                  # Prisma schema and migrations
├── public/                  # Static assets
├── src/
│   ├── app/                 # Next.js app directory
│   │   ├── api/             # API routes
│   │   ├── projects/        # Project pages
│   │   └── ...              # Other app routes
│   ├── components/          # React components
│   ├── lib/                 # Utility functions and libraries
│   └── generated/           # Generated Prisma client
├── .env                     # Environment variables
└── ...                      # Config files
```

## Database Schema

The application uses the following main models:

- **Project**: Core entity with project details
- **Task**: Tasks associated with projects
- **User**: User information for authentication
- **Comment**: Comments on projects

## Future Enhancements

- User authentication and role-based access control
- Advanced filtering and search capabilities
- File attachments for projects
- Email notifications
- Analytics and reporting dashboard
- Dark mode toggle

## License

[MIT](LICENSE)
