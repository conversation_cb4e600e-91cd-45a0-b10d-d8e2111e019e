"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AppLayout */ \"(app-pages-browser)/./src/components/AppLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PaintBrushIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,LinkIcon,PaintBrushIcon,ShieldCheckIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LinkIcon.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_user_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/user-utils */ \"(app-pages-browser)/./src/lib/user-utils.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _profile_firstName, _profile_lastName;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Theme state\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [compactMode, setCompactMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [highContrast, setHighContrast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Security form state\n    const [currentPassword, setCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Departments state\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentsLoading, setDepartmentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Profile form state\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        jobTitle: '',\n        department: '',\n        departmentId: '',\n        phone: '',\n        bio: ''\n    });\n    // Load user preferences from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (true) {\n                // Load theme settings\n                const savedTheme = localStorage.getItem('app-theme') || 'light';\n                setTheme(savedTheme);\n                document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n                // Load other appearance settings\n                setCompactMode(localStorage.getItem('compact-mode') === 'true');\n                setHighContrast(localStorage.getItem('high-contrast') === 'true');\n                // Apply compact mode if enabled\n                document.documentElement.classList.toggle('compact', compactMode);\n                // Apply high contrast if enabled\n                document.documentElement.classList.toggle('high-contrast', highContrast);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Fetch departments\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            const fetchDepartments = {\n                \"SettingsPage.useEffect.fetchDepartments\": async ()=>{\n                    try {\n                        setDepartmentsLoading(true);\n                        const response = await fetch('/api/departments');\n                        if (!response.ok) {\n                            throw new Error(\"Error \".concat(response.status, \": \").concat(response.statusText));\n                        }\n                        const data = await response.json();\n                        setDepartments(data);\n                    } catch (error) {\n                        console.error('Failed to fetch departments:', error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('Failed to load departments. Using default values instead.');\n                    } finally{\n                        setDepartmentsLoading(false);\n                    }\n                }\n            }[\"SettingsPage.useEffect.fetchDepartments\"];\n            fetchDepartments();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    // Load user data from session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                const fullName = session.user.name || '';\n                const nameParts = fullName.split(' ');\n                const firstName = nameParts[0] || '';\n                const lastName = nameParts.slice(1).join(' ') || '';\n                setProfile({\n                    \"SettingsPage.useEffect\": (prev)=>({\n                            ...prev,\n                            firstName,\n                            lastName,\n                            email: session.user.email || '',\n                            // Keep existing values for fields not in session or set defaults\n                            jobTitle: prev.jobTitle || 'Project Manager',\n                            department: prev.department || '',\n                            departmentId: prev.departmentId || '',\n                            phone: prev.phone || '',\n                            bio: prev.bio || \"\".concat(session.user.role || 'Team member', \" with expertise in project management.\")\n                        })\n                }[\"SettingsPage.useEffect\"]);\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        session\n    ]);\n    // Handle theme change\n    const handleThemeChange = (newTheme)=>{\n        setTheme(newTheme);\n        localStorage.setItem('app-theme', newTheme);\n        // Apply theme to document\n        if (newTheme === 'dark') {\n            document.documentElement.classList.add('dark');\n        } else if (newTheme === 'light') {\n            document.documentElement.classList.remove('dark');\n        } else if (newTheme === 'auto') {\n            // Check system preference\n            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n            document.documentElement.classList.toggle('dark', prefersDark);\n        }\n    };\n    // Handle compact mode toggle\n    const handleCompactModeToggle = ()=>{\n        const newValue = !compactMode;\n        setCompactMode(newValue);\n        localStorage.setItem('compact-mode', String(newValue));\n        document.documentElement.classList.toggle('compact', newValue);\n    };\n    // Handle high contrast toggle\n    const handleHighContrastToggle = ()=>{\n        const newValue = !highContrast;\n        setHighContrast(newValue);\n        localStorage.setItem('high-contrast', String(newValue));\n        document.documentElement.classList.toggle('high-contrast', newValue);\n    };\n    // Handle password update\n    const handlePasswordUpdate = async (e)=>{\n        e.preventDefault();\n        setPasswordError('');\n        // Validation\n        if (newPassword !== confirmPassword) {\n            setPasswordError('New passwords do not match');\n            return;\n        }\n        if (newPassword.length < 6) {\n            setPasswordError('Password must be at least 6 characters');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const user = _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser;\n            if (!user || !user.email) {\n                throw new Error('No authenticated user found');\n            }\n            // Re-authenticate user before changing password\n            const credential = firebase_auth__WEBPACK_IMPORTED_MODULE_6__.EmailAuthProvider.credential(user.email, currentPassword);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.reauthenticateWithCredential)(user, credential);\n            // Update password\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_6__.updatePassword)(user, newPassword);\n            // Clear form\n            setCurrentPassword('');\n            setNewPassword('');\n            setConfirmPassword('');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Password updated successfully');\n        } catch (error) {\n            console.error('Error updating password:', error);\n            // Handle Firebase auth errors\n            if (error instanceof Error) {\n                const authError = error;\n                if (authError.code === 'auth/wrong-password') {\n                    setPasswordError('Current password is incorrect');\n                } else if (authError.code === 'auth/weak-password') {\n                    setPasswordError('Password is too weak');\n                } else {\n                    setPasswordError('Failed to update password. Please try again.');\n                }\n            } else {\n                setPasswordError('An unexpected error occurred');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Handle profile update\n    const handleProfileUpdate = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            var _session_user;\n            // Validate form data\n            if (!profile.firstName.trim() || !profile.lastName.trim()) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('First name and last name are required');\n                return;\n            }\n            if (!profile.email.trim() || !profile.email.includes('@')) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('Please enter a valid email address');\n                return;\n            }\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                // Update user profile via API\n                const response = await fetch('/api/user/profile', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: \"\".concat(profile.firstName.trim(), \" \").concat(profile.lastName.trim()),\n                        email: profile.email.trim(),\n                        department: profile.department,\n                        departmentId: profile.departmentId,\n                        phone: profile.phone,\n                        bio: profile.bio,\n                        jobTitle: profile.jobTitle\n                    })\n                });\n                const data = await response.json();\n                if (!response.ok) {\n                    throw new Error(data.error || 'Failed to update profile');\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Profile updated successfully');\n                // Trigger session update to reflect name changes in UI\n                if (true) {\n                    window.location.reload();\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('No user session found');\n            }\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('Failed to update profile');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Request password reset\n    const handlePasswordReset = async ()=>{\n        try {\n            var _session_user;\n            if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email) {\n                await (0,_lib_user_utils__WEBPACK_IMPORTED_MODULE_7__.sendPasswordReset)(session.user.email);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success('Password reset email sent');\n            }\n        } catch (error) {\n            console.error('Error sending password reset:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error('Failed to send password reset email');\n        }\n    };\n    const tabs = [\n        {\n            id: 'profile',\n            label: 'Profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 46\n            }, this)\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            id: 'appearance',\n            label: 'Appearance',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 48\n            }, this)\n        },\n        {\n            id: 'integrations',\n            label: 'Integrations',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_LinkIcon_PaintBrushIcon_ShieldCheckIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 56\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Settings\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                            children: \"Settings\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-slate-500 dark:text-slate-400 mt-1\",\n                            children: \"Manage your account settings and preferences\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-slate-50 dark:bg-slate-800 p-6 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-1\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"w-full flex items-center text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors \".concat(activeTab === tab.id ? 'bg-blue-500 text-white' : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3\",\n                                                    children: tab.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tab.label\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-3 p-6\",\n                                children: [\n                                    activeTab === 'profile' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Profile Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                className: \"space-y-6\",\n                                                onSubmit: handleProfileUpdate,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-20 w-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl font-medium mr-6\",\n                                                                children: [\n                                                                    ((_profile_firstName = profile.firstName) === null || _profile_firstName === void 0 ? void 0 : _profile_firstName[0]) || 'U',\n                                                                    ((_profile_lastName = profile.lastName) === null || _profile_lastName === void 0 ? void 0 : _profile_lastName[0]) || ''\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-base font-medium text-slate-900 dark:text-white mb-1\",\n                                                                        children: \"Profile Picture\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement file upload\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"])('Profile picture upload coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Upload new\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"text-sm text-slate-500 dark:text-slate-400 hover:text-slate-600 dark:hover:text-slate-300\",\n                                                                                onClick: ()=>{\n                                                                                    // TODO: Implement remove picture\n                                                                                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"])('Remove picture feature coming soon!', {\n                                                                                        icon: 'ℹ️'\n                                                                                    });\n                                                                                },\n                                                                                children: \"Remove\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"First Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 38\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.firstName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.firstName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                firstName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your first name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.firstName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"First name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Last Name \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white \".concat(!profile.lastName.trim() ? 'border-red-300 focus:border-red-500' : ''),\n                                                                        value: profile.lastName,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                lastName: e.target.value\n                                                                            }),\n                                                                        required: true,\n                                                                        placeholder: \"Enter your last name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    !profile.lastName.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Last name is required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: [\n                                                                            \"Email \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"email\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white bg-slate-50 dark:bg-slate-900\",\n                                                                        value: profile.email,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                email: e.target.value\n                                                                            }),\n                                                                        disabled: true,\n                                                                        title: \"Email cannot be changed\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Email cannot be changed. Contact administrator if needed.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Job Title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.jobTitle,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                jobTitle: e.target.value\n                                                                            }),\n                                                                        placeholder: \"e.g., Project Manager, Developer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.departmentId || '',\n                                                                        onChange: (e)=>{\n                                                                            const deptId = e.target.value;\n                                                                            const selectedDept = departments.find((d)=>d.id === deptId);\n                                                                            setProfile({\n                                                                                ...profile,\n                                                                                departmentId: deptId,\n                                                                                department: (selectedDept === null || selectedDept === void 0 ? void 0 : selectedDept.name) || ''\n                                                                            });\n                                                                        },\n                                                                        disabled: departmentsLoading,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"Select Department\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            departments.map((dept)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: dept.id,\n                                                                                    children: dept.name\n                                                                                }, dept.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    departmentsLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: \"Loading departments...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"tel\",\n                                                                        className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        value: profile.phone,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                phone: e.target.value\n                                                                            }),\n                                                                        placeholder: \"+968 9123 4567\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"md:col-span-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                        children: \"Bio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        className: \"input min-h-[100px] dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                        rows: 4,\n                                                                        value: profile.bio,\n                                                                        onChange: (e)=>setProfile({\n                                                                                ...profile,\n                                                                                bio: e.target.value\n                                                                            }),\n                                                                        placeholder: \"Tell us about yourself, your experience, and expertise...\",\n                                                                        maxLength: 500\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-500 dark:text-slate-400 text-xs mt-1\",\n                                                                        children: [\n                                                                            profile.bio.length,\n                                                                            \"/500 characters\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center pt-4 border-t border-slate-200 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500 dark:text-slate-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-red-500\",\n                                                                        children: \"*\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Required fields\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"btn btn-secondary\",\n                                                                        onClick: ()=>{\n                                                                            // Reset form to original values\n                                                                            if (session === null || session === void 0 ? void 0 : session.user) {\n                                                                                const fullName = session.user.name || '';\n                                                                                const nameParts = fullName.split(' ');\n                                                                                const firstName = nameParts[0] || '';\n                                                                                const lastName = nameParts.slice(1).join(' ') || '';\n                                                                                setProfile({\n                                                                                    firstName,\n                                                                                    lastName,\n                                                                                    email: session.user.email || '',\n                                                                                    jobTitle: 'Project Manager',\n                                                                                    department: '',\n                                                                                    departmentId: '',\n                                                                                    phone: '',\n                                                                                    bio: \"\".concat(session.user.role || 'Team member', \" with expertise in project management.\")\n                                                                                });\n                                                                            }\n                                                                        },\n                                                                        children: \"Reset\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"submit\",\n                                                                        className: \"btn btn-primary\",\n                                                                        disabled: isLoading || !profile.firstName.trim() || !profile.lastName.trim(),\n                                                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                            className: \"opacity-25\",\n                                                                                            cx: \"12\",\n                                                                                            cy: \"12\",\n                                                                                            r: \"10\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            strokeWidth: \"4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 537,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            className: \"opacity-75\",\n                                                                                            fill: \"currentColor\",\n                                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 538,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 536,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Saving...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 29\n                                                                        }, this) : 'Save Changes'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    {\n                                                        title: 'Email Notifications',\n                                                        desc: 'Receive notifications via email'\n                                                    },\n                                                    {\n                                                        title: 'Project Updates',\n                                                        desc: 'Get notified when projects are updated'\n                                                    },\n                                                    {\n                                                        title: 'Task Assignments',\n                                                        desc: 'Notifications for new task assignments'\n                                                    },\n                                                    {\n                                                        title: 'Deadline Reminders',\n                                                        desc: 'Reminders for upcoming deadlines'\n                                                    },\n                                                    {\n                                                        title: 'Team Mentions',\n                                                        desc: 'When someone mentions you in comments'\n                                                    }\n                                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-4 border-b border-slate-200 dark:border-slate-700 last:border-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                        children: item.desc\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"sr-only peer\",\n                                                                        defaultChecked: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'appearance' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Appearance Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                children: [\n                                                                    {\n                                                                        name: 'Light',\n                                                                        desc: 'Clean and bright interface',\n                                                                        value: 'light'\n                                                                    },\n                                                                    {\n                                                                        name: 'Dark',\n                                                                        desc: 'Easy on the eyes',\n                                                                        value: 'dark'\n                                                                    },\n                                                                    {\n                                                                        name: 'Auto',\n                                                                        desc: 'Matches system preference',\n                                                                        value: 'auto'\n                                                                    }\n                                                                ].map((themeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card p-4 cursor-pointer border-2 \".concat(theme === themeOption.value ? 'border-blue-500' : 'border-transparent', \" dark:bg-slate-800\"),\n                                                                        onClick: ()=>handleThemeChange(themeOption.value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                children: themeOption.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                children: themeOption.desc\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 596,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, themeOption.value, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Display Options\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"Compact Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Reduce spacing for more content\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 608,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: compactMode,\n                                                                                        onChange: handleCompactModeToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 611,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 617,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 610,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"font-medium text-slate-900 dark:text-white\",\n                                                                                        children: \"High Contrast\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                                        children: \"Increase contrast for better visibility\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 624,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"relative inline-flex items-center cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        className: \"sr-only peer\",\n                                                                                        checked: highContrast,\n                                                                                        onChange: handleHighContrastToggle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 627,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 626,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Change Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 647,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                                onSubmit: handlePasswordUpdate,\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Current Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 650,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: currentPassword,\n                                                                                onChange: (e)=>setCurrentPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 651,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 649,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 660,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: newPassword,\n                                                                                onChange: (e)=>setNewPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2\",\n                                                                                children: \"Confirm New Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                className: \"input dark:bg-slate-800 dark:border-slate-700 dark:text-white\",\n                                                                                value: confirmPassword,\n                                                                                onChange: (e)=>setConfirmPassword(e.target.value),\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 669,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-red-500 text-sm\",\n                                                                        children: passwordError\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 681,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"submit\",\n                                                                                className: \"btn btn-primary\",\n                                                                                disabled: isLoading,\n                                                                                children: isLoading ? 'Updating...' : 'Update Password'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                className: \"btn btn-secondary\",\n                                                                                onClick: handlePasswordReset,\n                                                                                children: \"Send Reset Email\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 692,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-slate-900 dark:text-white mb-4\",\n                                                                children: \"Login Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 704,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-600 dark:text-slate-400 mb-4\",\n                                                                children: \"Monitor your account login activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-slate-50 dark:bg-slate-700 p-3 rounded-lg mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-slate-900 dark:text-white font-medium\",\n                                                                        children: \"Current Session\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            \"Active now - \",\n                                                                            new Date().toLocaleString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"btn btn-secondary text-sm\",\n                                                                children: \"View All Activity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 17\n                                    }, this),\n                                    activeTab === 'integrations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-slate-900 dark:text-white mb-6\",\n                                                children: \"Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    {\n                                                        name: 'Slack',\n                                                        desc: 'Connect with your Slack workspace',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Microsoft Teams',\n                                                        desc: 'Integrate with Teams for notifications',\n                                                        connected: false\n                                                    },\n                                                    {\n                                                        name: 'Google Calendar',\n                                                        desc: 'Sync project deadlines with calendar',\n                                                        connected: true\n                                                    },\n                                                    {\n                                                        name: 'Jira',\n                                                        desc: 'Import and sync Jira issues',\n                                                        connected: false\n                                                    }\n                                                ].map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"card p-4 dark:bg-slate-800 dark:border-slate-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                                            children: integration.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-slate-600 dark:text-slate-400\",\n                                                                            children: integration.desc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn \".concat(integration.connected ? 'btn-secondary' : 'btn-primary'),\n                                                                    children: integration.connected ? 'Disconnect' : 'Connect'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 719,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"IysQmi3MYF+YSWwMev/AVTT0jvw=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});