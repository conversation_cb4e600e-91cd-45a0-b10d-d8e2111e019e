/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/team/route";
exports.ids = ["app/api/team/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam%2Froute&page=%2Fapi%2Fteam%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam%2Froute&page=%2Fapi%2Fteam%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_team_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/team/route.ts */ \"(rsc)/./src/app/api/team/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/team/route\",\n        pathname: \"/api/team\",\n        filename: \"route\",\n        bundlePath: \"app/api/team/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\project-management-app\\\\project-management-app\\\\src\\\\app\\\\api\\\\team\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mohd9_Downloads_project_management_app_project_management_app_src_app_api_team_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam%2Froute&page=%2Fapi%2Fteam%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/team/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/team/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_userService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/userService */ \"(rsc)/./src/services/userService.ts\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Get all team members\nasync function GET() {\n    try {\n        const users = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_1__.getAllUsers)();\n        // Remove password from response\n        const safeUsers = users.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        });\n        // Get project counts for each user\n        const projectsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'projects');\n        const usersWithProjects = await Promise.all(safeUsers.map(async (user)=>{\n            try {\n                // Query projects where opdFocal matches user name\n                const projectsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(projectsCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)('opdFocal', '==', user.name));\n                const projectsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(projectsQuery);\n                // Return user with projects count\n                return {\n                    ...user,\n                    projectCount: projectsSnapshot.size,\n                    projects: projectsSnapshot.docs.map((doc)=>({\n                            id: doc.id,\n                            title: doc.data().projectTitle,\n                            status: doc.data().status,\n                            percentage: doc.data().percentage\n                        }))\n                };\n            } catch (error) {\n                console.error(`Error fetching projects for user ${user.id}:`, error);\n                return {\n                    ...user,\n                    projectCount: 0,\n                    projects: []\n                };\n            }\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(usersWithProjects);\n    } catch (error) {\n        console.error('Error fetching team members:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch team members'\n        }, {\n            status: 500\n        });\n    }\n}\n// Create a new team member\nasync function POST(request) {\n    try {\n        // For now, skip authentication check since we're transitioning\n        // const session = await getServerSession(authOptions);\n        // if (!session?.user?.role || session.user.role !== 'ADMIN') {\n        //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n        // }\n        const body = await request.json();\n        const { name, email, password, role, department, departmentId } = body;\n        if (!name || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already exists\n        const existingUser = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_1__.getUserByEmail)(email);\n        if (existingUser) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email already exists'\n            }, {\n                status: 400\n            });\n        }\n        // Hash password\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().hash(password, 10);\n        // Create user in Firebase\n        const user = await (0,_services_userService__WEBPACK_IMPORTED_MODULE_1__.createUser)({\n            name,\n            email,\n            password: hashedPassword,\n            role: role || 'USER',\n            department: department || '',\n            departmentId: departmentId || null\n        });\n        // Remove password from response\n        const { password: _, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(userWithoutPassword);\n    } catch (error) {\n        console.error('Error creating team member:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create team member'\n        }, {\n            status: 500\n        });\n    }\n}\n// Update a team member\nasync function PUT(request) {\n    try {\n        // For now, skip authentication check since we're transitioning\n        // const session = await getServerSession(authOptions);\n        // if (!session?.user?.role || session.user.role !== 'ADMIN') {\n        //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n        // }\n        const body = await request.json();\n        const { id, name, email, role, department, departmentId, password } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing user ID'\n            }, {\n                status: 400\n            });\n        }\n        const updateData = {\n            name,\n            email,\n            role,\n            department: department || '',\n            departmentId: departmentId || null\n        };\n        // Only update password if provided\n        if (password) {\n            updateData.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().hash(password, 10);\n        }\n        await (0,_services_userService__WEBPACK_IMPORTED_MODULE_1__.updateUser)(id, updateData);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'User updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating team member:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update team member'\n        }, {\n            status: 500\n        });\n    }\n}\n// Delete a team member\nasync function DELETE(request) {\n    try {\n        // For now, skip authentication check since we're transitioning\n        // const session = await getServerSession(authOptions);\n        // if (!session?.user?.role || session.user.role !== 'ADMIN') {\n        //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n        // }\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing user ID'\n            }, {\n                status: 400\n            });\n        }\n        await (0,_services_userService__WEBPACK_IMPORTED_MODULE_1__.deleteUser)(id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Team member deleted successfully'\n        });\n    } catch (error) {\n        console.error('Error deleting team member:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete team member'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS90ZWFtL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBMkM7QUFTWDtBQUNJO0FBQ21DO0FBQ3pDO0FBRTlCLHVCQUF1QjtBQUNoQixlQUFlWTtJQUNwQixJQUFJO1FBQ0YsTUFBTUMsUUFBUSxNQUFNWixrRUFBV0E7UUFFL0IsZ0NBQWdDO1FBQ2hDLE1BQU1hLFlBQVlELE1BQU1FLEdBQUcsQ0FBQ0MsQ0FBQUE7WUFDMUIsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0MscUJBQXFCLEdBQUdGO1lBQzdDLE9BQU9FO1FBQ1Q7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTUMscUJBQXFCWiw4REFBVUEsQ0FBQ0QsNkNBQUVBLEVBQUU7UUFDMUMsTUFBTWMsb0JBQW9CLE1BQU1DLFFBQVFDLEdBQUcsQ0FDekNSLFVBQVVDLEdBQUcsQ0FBQyxPQUFPQztZQUNuQixJQUFJO2dCQUNGLGtEQUFrRDtnQkFDbEQsTUFBTU8sZ0JBQWdCZix5REFBS0EsQ0FDekJXLG9CQUNBVix5REFBS0EsQ0FBQyxZQUFZLE1BQU1PLEtBQUtRLElBQUk7Z0JBRW5DLE1BQU1DLG1CQUFtQixNQUFNZiwyREFBT0EsQ0FBQ2E7Z0JBRXZDLGtDQUFrQztnQkFDbEMsT0FBTztvQkFDTCxHQUFHUCxJQUFJO29CQUNQVSxjQUFjRCxpQkFBaUJFLElBQUk7b0JBQ25DQyxVQUFVSCxpQkFBaUJJLElBQUksQ0FBQ2QsR0FBRyxDQUFDZSxDQUFBQSxNQUFROzRCQUMxQ0MsSUFBSUQsSUFBSUMsRUFBRTs0QkFDVkMsT0FBT0YsSUFBSUcsSUFBSSxHQUFHQyxZQUFZOzRCQUM5QkMsUUFBUUwsSUFBSUcsSUFBSSxHQUFHRSxNQUFNOzRCQUN6QkMsWUFBWU4sSUFBSUcsSUFBSSxHQUFHRyxVQUFVO3dCQUNuQztnQkFDRjtZQUNGLEVBQUUsT0FBT0MsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsaUNBQWlDLEVBQUVyQixLQUFLZSxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUVNO2dCQUM5RCxPQUFPO29CQUNMLEdBQUdyQixJQUFJO29CQUNQVSxjQUFjO29CQUNkRSxVQUFVLEVBQUU7Z0JBQ2Q7WUFDRjtRQUNGO1FBR0YsT0FBTzVCLHFEQUFZQSxDQUFDdUMsSUFBSSxDQUFDbkI7SUFDM0IsRUFBRSxPQUFPaUIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPckMscURBQVlBLENBQUN1QyxJQUFJLENBQUM7WUFBRUYsT0FBTztRQUErQixHQUFHO1lBQUVGLFFBQVE7UUFBSTtJQUNwRjtBQUNGO0FBRUEsMkJBQTJCO0FBQ3BCLGVBQWVLLEtBQUtDLE9BQWdCO0lBQ3pDLElBQUk7UUFDRiwrREFBK0Q7UUFDL0QsdURBQXVEO1FBQ3ZELCtEQUErRDtRQUMvRCwwRUFBMEU7UUFDMUUsSUFBSTtRQUVKLE1BQU1DLE9BQU8sTUFBTUQsUUFBUUYsSUFBSTtRQUMvQixNQUFNLEVBQUVmLElBQUksRUFBRW1CLEtBQUssRUFBRTFCLFFBQVEsRUFBRTJCLElBQUksRUFBRUMsVUFBVSxFQUFFQyxZQUFZLEVBQUUsR0FBR0o7UUFFbEUsSUFBSSxDQUFDbEIsUUFBUSxDQUFDbUIsU0FBUyxDQUFDMUIsVUFBVTtZQUNoQyxPQUFPakIscURBQVlBLENBQUN1QyxJQUFJLENBQUM7Z0JBQUVGLE9BQU87WUFBMEIsR0FBRztnQkFBRUYsUUFBUTtZQUFJO1FBQy9FO1FBRUEsK0JBQStCO1FBQy9CLE1BQU1ZLGVBQWUsTUFBTTFDLHFFQUFjQSxDQUFDc0M7UUFDMUMsSUFBSUksY0FBYztZQUNoQixPQUFPL0MscURBQVlBLENBQUN1QyxJQUFJLENBQUM7Z0JBQUVGLE9BQU87WUFBdUIsR0FBRztnQkFBRUYsUUFBUTtZQUFJO1FBQzVFO1FBRUEsZ0JBQWdCO1FBQ2hCLE1BQU1hLGlCQUFpQixNQUFNckMsb0RBQVcsQ0FBQ00sVUFBVTtRQUVuRCwwQkFBMEI7UUFDMUIsTUFBTUQsT0FBTyxNQUFNZCxpRUFBVUEsQ0FBQztZQUM1QnNCO1lBQ0FtQjtZQUNBMUIsVUFBVStCO1lBQ1ZKLE1BQU1BLFFBQVE7WUFDZEMsWUFBWUEsY0FBYztZQUMxQkMsY0FBY0EsZ0JBQWdCO1FBQ2hDO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU0sRUFBRTdCLFVBQVVpQyxDQUFDLEVBQUUsR0FBR2hDLHFCQUFxQixHQUFHRjtRQUVoRCxPQUFPaEIscURBQVlBLENBQUN1QyxJQUFJLENBQUNyQjtJQUMzQixFQUFFLE9BQU9tQixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU9yQyxxREFBWUEsQ0FBQ3VDLElBQUksQ0FBQztZQUFFRixPQUFPO1FBQStCLEdBQUc7WUFBRUYsUUFBUTtRQUFJO0lBQ3BGO0FBQ0Y7QUFFQSx1QkFBdUI7QUFDaEIsZUFBZWdCLElBQUlWLE9BQWdCO0lBQ3hDLElBQUk7UUFDRiwrREFBK0Q7UUFDL0QsdURBQXVEO1FBQ3ZELCtEQUErRDtRQUMvRCwwRUFBMEU7UUFDMUUsSUFBSTtRQUVKLE1BQU1DLE9BQU8sTUFBTUQsUUFBUUYsSUFBSTtRQUMvQixNQUFNLEVBQUVSLEVBQUUsRUFBRVAsSUFBSSxFQUFFbUIsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLFVBQVUsRUFBRUMsWUFBWSxFQUFFN0IsUUFBUSxFQUFFLEdBQUd5QjtRQUV0RSxJQUFJLENBQUNYLElBQUk7WUFDUCxPQUFPL0IscURBQVlBLENBQUN1QyxJQUFJLENBQUM7Z0JBQUVGLE9BQU87WUFBa0IsR0FBRztnQkFBRUYsUUFBUTtZQUFJO1FBQ3ZFO1FBRUEsTUFBTWlCLGFBQWtCO1lBQ3RCNUI7WUFDQW1CO1lBQ0FDO1lBQ0FDLFlBQVlBLGNBQWM7WUFDMUJDLGNBQWNBLGdCQUFnQjtRQUNoQztRQUVBLG1DQUFtQztRQUNuQyxJQUFJN0IsVUFBVTtZQUNabUMsV0FBV25DLFFBQVEsR0FBRyxNQUFNTixvREFBVyxDQUFDTSxVQUFVO1FBQ3BEO1FBRUEsTUFBTWQsaUVBQVVBLENBQUM0QixJQUFJcUI7UUFFckIsT0FBT3BELHFEQUFZQSxDQUFDdUMsSUFBSSxDQUFDO1lBQUVjLFNBQVM7UUFBNEI7SUFDbEUsRUFBRSxPQUFPaEIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPckMscURBQVlBLENBQUN1QyxJQUFJLENBQUM7WUFBRUYsT0FBTztRQUErQixHQUFHO1lBQUVGLFFBQVE7UUFBSTtJQUNwRjtBQUNGO0FBRUEsdUJBQXVCO0FBQ2hCLGVBQWVtQixPQUFPYixPQUFnQjtJQUMzQyxJQUFJO1FBQ0YsK0RBQStEO1FBQy9ELHVEQUF1RDtRQUN2RCwrREFBK0Q7UUFDL0QsMEVBQTBFO1FBQzFFLElBQUk7UUFFSixNQUFNLEVBQUVjLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlmLFFBQVFnQixHQUFHO1FBQzVDLE1BQU0xQixLQUFLd0IsYUFBYUcsR0FBRyxDQUFDO1FBRTVCLElBQUksQ0FBQzNCLElBQUk7WUFDUCxPQUFPL0IscURBQVlBLENBQUN1QyxJQUFJLENBQUM7Z0JBQUVGLE9BQU87WUFBa0IsR0FBRztnQkFBRUYsUUFBUTtZQUFJO1FBQ3ZFO1FBRUEsTUFBTS9CLGlFQUFVQSxDQUFDMkI7UUFFakIsT0FBTy9CLHFEQUFZQSxDQUFDdUMsSUFBSSxDQUFDO1lBQUVjLFNBQVM7UUFBbUM7SUFDekUsRUFBRSxPQUFPaEIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPckMscURBQVlBLENBQUN1QyxJQUFJLENBQUM7WUFBRUYsT0FBTztRQUErQixHQUFHO1lBQUVGLFFBQVE7UUFBSTtJQUNwRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vaGQ5XFxEb3dubG9hZHNcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHNyY1xcYXBwXFxhcGlcXHRlYW1cXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcclxuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aCc7XHJcbmltcG9ydCB7IGF1dGhPcHRpb25zIH0gZnJvbSAnQC9saWIvYXV0aCc7XHJcbmltcG9ydCB7IFxyXG4gIGdldEFsbFVzZXJzLCBcclxuICBjcmVhdGVVc2VyLCBcclxuICB1cGRhdGVVc2VyLCBcclxuICBkZWxldGVVc2VyLFxyXG4gIGdldFVzZXJCeUVtYWlsXHJcbn0gZnJvbSAnQC9zZXJ2aWNlcy91c2VyU2VydmljZSc7XHJcbmltcG9ydCB7IGRiIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xyXG5pbXBvcnQgeyBjb2xsZWN0aW9uLCBxdWVyeSwgd2hlcmUsIGdldERvY3MgfSBmcm9tICdmaXJlYmFzZS9maXJlc3RvcmUnO1xyXG5pbXBvcnQgYmNyeXB0IGZyb20gJ2JjcnlwdGpzJztcclxuXHJcbi8vIEdldCBhbGwgdGVhbSBtZW1iZXJzXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHVzZXJzID0gYXdhaXQgZ2V0QWxsVXNlcnMoKTtcclxuICAgIFxyXG4gICAgLy8gUmVtb3ZlIHBhc3N3b3JkIGZyb20gcmVzcG9uc2VcclxuICAgIGNvbnN0IHNhZmVVc2VycyA9IHVzZXJzLm1hcCh1c2VyID0+IHtcclxuICAgICAgY29uc3QgeyBwYXNzd29yZCwgLi4udXNlcldpdGhvdXRQYXNzd29yZCB9ID0gdXNlcjtcclxuICAgICAgcmV0dXJuIHVzZXJXaXRob3V0UGFzc3dvcmQ7XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBHZXQgcHJvamVjdCBjb3VudHMgZm9yIGVhY2ggdXNlclxyXG4gICAgY29uc3QgcHJvamVjdHNDb2xsZWN0aW9uID0gY29sbGVjdGlvbihkYiwgJ3Byb2plY3RzJyk7XHJcbiAgICBjb25zdCB1c2Vyc1dpdGhQcm9qZWN0cyA9IGF3YWl0IFByb21pc2UuYWxsKFxyXG4gICAgICBzYWZlVXNlcnMubWFwKGFzeW5jICh1c2VyKSA9PiB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIC8vIFF1ZXJ5IHByb2plY3RzIHdoZXJlIG9wZEZvY2FsIG1hdGNoZXMgdXNlciBuYW1lXHJcbiAgICAgICAgICBjb25zdCBwcm9qZWN0c1F1ZXJ5ID0gcXVlcnkoXHJcbiAgICAgICAgICAgIHByb2plY3RzQ29sbGVjdGlvbiwgXHJcbiAgICAgICAgICAgIHdoZXJlKCdvcGRGb2NhbCcsICc9PScsIHVzZXIubmFtZSlcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBjb25zdCBwcm9qZWN0c1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhwcm9qZWN0c1F1ZXJ5KTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gUmV0dXJuIHVzZXIgd2l0aCBwcm9qZWN0cyBjb3VudFxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgLi4udXNlcixcclxuICAgICAgICAgICAgcHJvamVjdENvdW50OiBwcm9qZWN0c1NuYXBzaG90LnNpemUsXHJcbiAgICAgICAgICAgIHByb2plY3RzOiBwcm9qZWN0c1NuYXBzaG90LmRvY3MubWFwKGRvYyA9PiAoe1xyXG4gICAgICAgICAgICAgIGlkOiBkb2MuaWQsXHJcbiAgICAgICAgICAgICAgdGl0bGU6IGRvYy5kYXRhKCkucHJvamVjdFRpdGxlLFxyXG4gICAgICAgICAgICAgIHN0YXR1czogZG9jLmRhdGEoKS5zdGF0dXMsXHJcbiAgICAgICAgICAgICAgcGVyY2VudGFnZTogZG9jLmRhdGEoKS5wZXJjZW50YWdlXHJcbiAgICAgICAgICAgIH0pKVxyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgcHJvamVjdHMgZm9yIHVzZXIgJHt1c2VyLmlkfTpgLCBlcnJvcik7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAuLi51c2VyLFxyXG4gICAgICAgICAgICBwcm9qZWN0Q291bnQ6IDAsXHJcbiAgICAgICAgICAgIHByb2plY3RzOiBbXVxyXG4gICAgICAgICAgfTtcclxuICAgICAgICB9XHJcbiAgICAgIH0pXHJcbiAgICApO1xyXG5cclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih1c2Vyc1dpdGhQcm9qZWN0cyk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHRlYW0gbWVtYmVyczonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBmZXRjaCB0ZWFtIG1lbWJlcnMnIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBDcmVhdGUgYSBuZXcgdGVhbSBtZW1iZXJcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogUmVxdWVzdCkge1xyXG4gIHRyeSB7XHJcbiAgICAvLyBGb3Igbm93LCBza2lwIGF1dGhlbnRpY2F0aW9uIGNoZWNrIHNpbmNlIHdlJ3JlIHRyYW5zaXRpb25pbmdcclxuICAgIC8vIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKTtcclxuICAgIC8vIGlmICghc2Vzc2lvbj8udXNlcj8ucm9sZSB8fCBzZXNzaW9uLnVzZXIucm9sZSAhPT0gJ0FETUlOJykge1xyXG4gICAgLy8gICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcclxuICAgIC8vIH1cclxuXHJcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XHJcbiAgICBjb25zdCB7IG5hbWUsIGVtYWlsLCBwYXNzd29yZCwgcm9sZSwgZGVwYXJ0bWVudCwgZGVwYXJ0bWVudElkIH0gPSBib2R5O1xyXG5cclxuICAgIGlmICghbmFtZSB8fCAhZW1haWwgfHwgIXBhc3N3b3JkKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnTWlzc2luZyByZXF1aXJlZCBmaWVsZHMnIH0sIHsgc3RhdHVzOiA0MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgdXNlciBhbHJlYWR5IGV4aXN0c1xyXG4gICAgY29uc3QgZXhpc3RpbmdVc2VyID0gYXdhaXQgZ2V0VXNlckJ5RW1haWwoZW1haWwpO1xyXG4gICAgaWYgKGV4aXN0aW5nVXNlcikge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0VtYWlsIGFscmVhZHkgZXhpc3RzJyB9LCB7IHN0YXR1czogNDAwIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEhhc2ggcGFzc3dvcmRcclxuICAgIGNvbnN0IGhhc2hlZFBhc3N3b3JkID0gYXdhaXQgYmNyeXB0Lmhhc2gocGFzc3dvcmQsIDEwKTtcclxuXHJcbiAgICAvLyBDcmVhdGUgdXNlciBpbiBGaXJlYmFzZVxyXG4gICAgY29uc3QgdXNlciA9IGF3YWl0IGNyZWF0ZVVzZXIoe1xyXG4gICAgICBuYW1lLFxyXG4gICAgICBlbWFpbCxcclxuICAgICAgcGFzc3dvcmQ6IGhhc2hlZFBhc3N3b3JkLFxyXG4gICAgICByb2xlOiByb2xlIHx8ICdVU0VSJyxcclxuICAgICAgZGVwYXJ0bWVudDogZGVwYXJ0bWVudCB8fCAnJyxcclxuICAgICAgZGVwYXJ0bWVudElkOiBkZXBhcnRtZW50SWQgfHwgbnVsbFxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gUmVtb3ZlIHBhc3N3b3JkIGZyb20gcmVzcG9uc2VcclxuICAgIGNvbnN0IHsgcGFzc3dvcmQ6IF8sIC4uLnVzZXJXaXRob3V0UGFzc3dvcmQgfSA9IHVzZXI7XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHVzZXJXaXRob3V0UGFzc3dvcmQpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyB0ZWFtIG1lbWJlcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgdGVhbSBtZW1iZXInIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBVcGRhdGUgYSB0ZWFtIG1lbWJlclxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUFVUKHJlcXVlc3Q6IFJlcXVlc3QpIHtcclxuICB0cnkge1xyXG4gICAgLy8gRm9yIG5vdywgc2tpcCBhdXRoZW50aWNhdGlvbiBjaGVjayBzaW5jZSB3ZSdyZSB0cmFuc2l0aW9uaW5nXHJcbiAgICAvLyBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucyk7XHJcbiAgICAvLyBpZiAoIXNlc3Npb24/LnVzZXI/LnJvbGUgfHwgc2Vzc2lvbi51c2VyLnJvbGUgIT09ICdBRE1JTicpIHtcclxuICAgIC8vICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSk7XHJcbiAgICAvLyB9XHJcblxyXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xyXG4gICAgY29uc3QgeyBpZCwgbmFtZSwgZW1haWwsIHJvbGUsIGRlcGFydG1lbnQsIGRlcGFydG1lbnRJZCwgcGFzc3dvcmQgfSA9IGJvZHk7XHJcblxyXG4gICAgaWYgKCFpZCkge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ01pc3NpbmcgdXNlciBJRCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBhbnkgPSB7XHJcbiAgICAgIG5hbWUsXHJcbiAgICAgIGVtYWlsLFxyXG4gICAgICByb2xlLFxyXG4gICAgICBkZXBhcnRtZW50OiBkZXBhcnRtZW50IHx8ICcnLFxyXG4gICAgICBkZXBhcnRtZW50SWQ6IGRlcGFydG1lbnRJZCB8fCBudWxsXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIE9ubHkgdXBkYXRlIHBhc3N3b3JkIGlmIHByb3ZpZGVkXHJcbiAgICBpZiAocGFzc3dvcmQpIHtcclxuICAgICAgdXBkYXRlRGF0YS5wYXNzd29yZCA9IGF3YWl0IGJjcnlwdC5oYXNoKHBhc3N3b3JkLCAxMCk7XHJcbiAgICB9XHJcblxyXG4gICAgYXdhaXQgdXBkYXRlVXNlcihpZCwgdXBkYXRlRGF0YSk7XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgbWVzc2FnZTogJ1VzZXIgdXBkYXRlZCBzdWNjZXNzZnVsbHknIH0pO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB0ZWFtIG1lbWJlcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byB1cGRhdGUgdGVhbSBtZW1iZXInIH0sIHsgc3RhdHVzOiA1MDAgfSk7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBEZWxldGUgYSB0ZWFtIG1lbWJlclxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gREVMRVRFKHJlcXVlc3Q6IFJlcXVlc3QpIHtcclxuICB0cnkge1xyXG4gICAgLy8gRm9yIG5vdywgc2tpcCBhdXRoZW50aWNhdGlvbiBjaGVjayBzaW5jZSB3ZSdyZSB0cmFuc2l0aW9uaW5nXHJcbiAgICAvLyBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucyk7XHJcbiAgICAvLyBpZiAoIXNlc3Npb24/LnVzZXI/LnJvbGUgfHwgc2Vzc2lvbi51c2VyLnJvbGUgIT09ICdBRE1JTicpIHtcclxuICAgIC8vICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sIHsgc3RhdHVzOiA0MDEgfSk7XHJcbiAgICAvLyB9XHJcblxyXG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xyXG4gICAgY29uc3QgaWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpO1xyXG5cclxuICAgIGlmICghaWQpIHtcclxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdNaXNzaW5nIHVzZXIgSUQnIH0sIHsgc3RhdHVzOiA0MDAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgYXdhaXQgZGVsZXRlVXNlcihpZCk7XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgbWVzc2FnZTogJ1RlYW0gbWVtYmVyIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5JyB9KTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdGVhbSBtZW1iZXI6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdGYWlsZWQgdG8gZGVsZXRlIHRlYW0gbWVtYmVyJyB9LCB7IHN0YXR1czogNTAwIH0pO1xyXG4gIH1cclxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXRBbGxVc2VycyIsImNyZWF0ZVVzZXIiLCJ1cGRhdGVVc2VyIiwiZGVsZXRlVXNlciIsImdldFVzZXJCeUVtYWlsIiwiZGIiLCJjb2xsZWN0aW9uIiwicXVlcnkiLCJ3aGVyZSIsImdldERvY3MiLCJiY3J5cHQiLCJHRVQiLCJ1c2VycyIsInNhZmVVc2VycyIsIm1hcCIsInVzZXIiLCJwYXNzd29yZCIsInVzZXJXaXRob3V0UGFzc3dvcmQiLCJwcm9qZWN0c0NvbGxlY3Rpb24iLCJ1c2Vyc1dpdGhQcm9qZWN0cyIsIlByb21pc2UiLCJhbGwiLCJwcm9qZWN0c1F1ZXJ5IiwibmFtZSIsInByb2plY3RzU25hcHNob3QiLCJwcm9qZWN0Q291bnQiLCJzaXplIiwicHJvamVjdHMiLCJkb2NzIiwiZG9jIiwiaWQiLCJ0aXRsZSIsImRhdGEiLCJwcm9qZWN0VGl0bGUiLCJzdGF0dXMiLCJwZXJjZW50YWdlIiwiZXJyb3IiLCJjb25zb2xlIiwianNvbiIsIlBPU1QiLCJyZXF1ZXN0IiwiYm9keSIsImVtYWlsIiwicm9sZSIsImRlcGFydG1lbnQiLCJkZXBhcnRtZW50SWQiLCJleGlzdGluZ1VzZXIiLCJoYXNoZWRQYXNzd29yZCIsImhhc2giLCJfIiwiUFVUIiwidXBkYXRlRGF0YSIsIm1lc3NhZ2UiLCJERUxFVEUiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJnZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/team/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(rsc)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(rsc)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(rsc)/./node_modules/firebase/storage/dist/index.mjs\");\n// Import the functions you need from the SDKs you need\n\n\n\n\n// Your web app's Firebase configuration\n// For Firebase JS SDK v7.20.0 and later, measurementId is optional\nconst firebaseConfig = {\n    apiKey: \"AIzaSyAkGE-lbEzcRVJZbKjE_SHJd38jENqut8k\",\n    authDomain: \"project-management-f45cc.firebaseapp.com\",\n    projectId: \"project-management-f45cc\",\n    storageBucket: \"project-management-f45cc.firebasestorage.app\",\n    messagingSenderId: \"1002222709659\",\n    appId: \"1:1002222709659:web:6b1ab479efcc4102824f3e\",\n    measurementId: \"G-JYYNYZV8LP\"\n};\n// Initialize Firebase only if we don't already have an instance\n// This helps prevent multiple initializations during SSR/SSG\nlet app;\nif (!(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length) {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n} else {\n    app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n}\n// Initialize Firebase services\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)(app);\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n// Analytics is now null by default\nconst analytics = null;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ZpcmViYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLHVEQUF1RDtBQUNEO0FBQ0o7QUFDVjtBQUNNO0FBRTlDLHdDQUF3QztBQUN4QyxtRUFBbUU7QUFDbkUsTUFBTUssaUJBQWlCO0lBQ3JCQyxRQUFRO0lBQ1JDLFlBQVk7SUFDWkMsV0FBVztJQUNYQyxlQUFlO0lBQ2ZDLG1CQUFtQjtJQUNuQkMsT0FBTztJQUNQQyxlQUFlO0FBQ2pCO0FBRUEsZ0VBQWdFO0FBQ2hFLDZEQUE2RDtBQUM3RCxJQUFJQztBQUNKLElBQUksQ0FBQ1oscURBQU9BLEdBQUdhLE1BQU0sRUFBRTtJQUNyQkQsTUFBTWIsMkRBQWFBLENBQUNLO0FBQ3RCLE9BQU87SUFDTFEsTUFBTVoscURBQU9BLEVBQUUsQ0FBQyxFQUFFO0FBQ3BCO0FBRUEsK0JBQStCO0FBQy9CLE1BQU1jLEtBQUtiLGdFQUFZQSxDQUFDVztBQUN4QixNQUFNRyxPQUFPYixzREFBT0EsQ0FBQ1U7QUFDckIsTUFBTUksVUFBVWIsNERBQVVBLENBQUNTO0FBRTNCLG1DQUFtQztBQUNuQyxNQUFNSyxZQUFZO0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1vaGQ5XFxEb3dubG9hZHNcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHByb2plY3QtbWFuYWdlbWVudC1hcHBcXHNyY1xcbGliXFxmaXJlYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJbXBvcnQgdGhlIGZ1bmN0aW9ucyB5b3UgbmVlZCBmcm9tIHRoZSBTREtzIHlvdSBuZWVkXHJcbmltcG9ydCB7IGluaXRpYWxpemVBcHAsIGdldEFwcHMgfSBmcm9tIFwiZmlyZWJhc2UvYXBwXCI7XHJcbmltcG9ydCB7IGdldEZpcmVzdG9yZSB9IGZyb20gXCJmaXJlYmFzZS9maXJlc3RvcmVcIjtcclxuaW1wb3J0IHsgZ2V0QXV0aCB9IGZyb20gXCJmaXJlYmFzZS9hdXRoXCI7XHJcbmltcG9ydCB7IGdldFN0b3JhZ2UgfSBmcm9tIFwiZmlyZWJhc2Uvc3RvcmFnZVwiO1xyXG5cclxuLy8gWW91ciB3ZWIgYXBwJ3MgRmlyZWJhc2UgY29uZmlndXJhdGlvblxyXG4vLyBGb3IgRmlyZWJhc2UgSlMgU0RLIHY3LjIwLjAgYW5kIGxhdGVyLCBtZWFzdXJlbWVudElkIGlzIG9wdGlvbmFsXHJcbmNvbnN0IGZpcmViYXNlQ29uZmlnID0ge1xyXG4gIGFwaUtleTogXCJBSXphU3lBa0dFLWxiRXpjUlZKWmJLakVfU0hKZDM4akVOcXV0OGtcIixcclxuICBhdXRoRG9tYWluOiBcInByb2plY3QtbWFuYWdlbWVudC1mNDVjYy5maXJlYmFzZWFwcC5jb21cIixcclxuICBwcm9qZWN0SWQ6IFwicHJvamVjdC1tYW5hZ2VtZW50LWY0NWNjXCIsXHJcbiAgc3RvcmFnZUJ1Y2tldDogXCJwcm9qZWN0LW1hbmFnZW1lbnQtZjQ1Y2MuZmlyZWJhc2VzdG9yYWdlLmFwcFwiLFxyXG4gIG1lc3NhZ2luZ1NlbmRlcklkOiBcIjEwMDIyMjI3MDk2NTlcIixcclxuICBhcHBJZDogXCIxOjEwMDIyMjI3MDk2NTk6d2ViOjZiMWFiNDc5ZWZjYzQxMDI4MjRmM2VcIixcclxuICBtZWFzdXJlbWVudElkOiBcIkctSllZTllaVjhMUFwiXHJcbn07XHJcblxyXG4vLyBJbml0aWFsaXplIEZpcmViYXNlIG9ubHkgaWYgd2UgZG9uJ3QgYWxyZWFkeSBoYXZlIGFuIGluc3RhbmNlXHJcbi8vIFRoaXMgaGVscHMgcHJldmVudCBtdWx0aXBsZSBpbml0aWFsaXphdGlvbnMgZHVyaW5nIFNTUi9TU0dcclxubGV0IGFwcDtcclxuaWYgKCFnZXRBcHBzKCkubGVuZ3RoKSB7XHJcbiAgYXBwID0gaW5pdGlhbGl6ZUFwcChmaXJlYmFzZUNvbmZpZyk7XHJcbn0gZWxzZSB7XHJcbiAgYXBwID0gZ2V0QXBwcygpWzBdO1xyXG59XHJcblxyXG4vLyBJbml0aWFsaXplIEZpcmViYXNlIHNlcnZpY2VzXHJcbmNvbnN0IGRiID0gZ2V0RmlyZXN0b3JlKGFwcCk7XHJcbmNvbnN0IGF1dGggPSBnZXRBdXRoKGFwcCk7XHJcbmNvbnN0IHN0b3JhZ2UgPSBnZXRTdG9yYWdlKGFwcCk7XHJcblxyXG4vLyBBbmFseXRpY3MgaXMgbm93IG51bGwgYnkgZGVmYXVsdFxyXG5jb25zdCBhbmFseXRpY3MgPSBudWxsO1xyXG5cclxuZXhwb3J0IHsgYXBwLCBkYiwgYXV0aCwgc3RvcmFnZSwgYW5hbHl0aWNzIH07ICJdLCJuYW1lcyI6WyJpbml0aWFsaXplQXBwIiwiZ2V0QXBwcyIsImdldEZpcmVzdG9yZSIsImdldEF1dGgiLCJnZXRTdG9yYWdlIiwiZmlyZWJhc2VDb25maWciLCJhcGlLZXkiLCJhdXRoRG9tYWluIiwicHJvamVjdElkIiwic3RvcmFnZUJ1Y2tldCIsIm1lc3NhZ2luZ1NlbmRlcklkIiwiYXBwSWQiLCJtZWFzdXJlbWVudElkIiwiYXBwIiwibGVuZ3RoIiwiZGIiLCJhdXRoIiwic3RvcmFnZSIsImFuYWx5dGljcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/userService.ts":
/*!*************************************!*\
  !*** ./src/services/userService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUsersByDepartment: () => (/* binding */ getUsersByDepartment),\n/* harmony export */   getUsersByRole: () => (/* binding */ getUsersByRole),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/firebase */ \"(rsc)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(rsc)/./node_modules/firebase/firestore/dist/index.mjs\");\n\n\n// Local implementation of toJsDate\nfunction toJsDate(date) {\n    if (!date) return null;\n    try {\n        // Already a Date object\n        if (date instanceof Date) {\n            return date;\n        }\n        // String date (ISO format or other string representation)\n        if (typeof date === 'string') {\n            // Handle ISO dates\n            if (date.match(/^\\d{4}-\\d{2}-\\d{2}/) || date.includes('T00:00:00')) {\n                const parsedDate = new Date(date);\n                if (!isNaN(parsedDate.getTime())) {\n                    return parsedDate;\n                }\n            }\n            // Try to parse as a date anyway\n            const parsedDate = new Date(date);\n            if (!isNaN(parsedDate.getTime())) {\n                return parsedDate;\n            }\n        }\n        // Numeric timestamp (milliseconds since epoch)\n        if (typeof date === 'number') {\n            return new Date(date);\n        }\n        // Firebase Timestamp with toDate() method\n        if (date && typeof date === 'object' && 'toDate' in date && typeof date.toDate === 'function') {\n            return date.toDate();\n        }\n        // Firebase Timestamp-like object with seconds and nanoseconds\n        if (date && typeof date === 'object' && 'seconds' in date) {\n            return new Date(date.seconds * 1000);\n        }\n        // Stringified object that might contain a timestamp\n        if (typeof date === 'string' && (date.includes('\"seconds\"') || date.includes('\"nanoseconds\"'))) {\n            try {\n                const parsed = JSON.parse(date);\n                if (parsed && typeof parsed === 'object' && 'seconds' in parsed) {\n                    return new Date(parsed.seconds * 1000);\n                }\n            } catch (e) {}\n        }\n    } catch (error) {\n        console.error('Error converting to JS Date:', error);\n    }\n    return null;\n}\n// Convert Firestore document to User object\nconst userConverter = {\n    fromFirestore (snapshot) {\n        const data = snapshot.data();\n        // Handle Firestore timestamps\n        let createdAt = new Date();\n        let updatedAt = new Date();\n        if (data.createdAt) {\n            createdAt = toJsDate(data.createdAt) || new Date();\n        }\n        if (data.updatedAt) {\n            updatedAt = toJsDate(data.updatedAt) || new Date();\n        }\n        return {\n            id: snapshot.id,\n            name: data.name || '',\n            email: data.email || '',\n            password: data.password || undefined,\n            role: data.role || 'USER',\n            department: data.department || null,\n            departmentId: data.departmentId || null,\n            createdAt: createdAt,\n            updatedAt: updatedAt\n        };\n    },\n    toFirestore (user) {\n        const { password, ...userData } = user;\n        return {\n            ...userData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n    }\n};\n// Collection reference\nconst usersCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users');\n// Get all users\nasync function getAllUsers() {\n    try {\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(usersCollection);\n        const users = snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n        // Fetch department names for each user\n        const usersWithDepartments = await Promise.all(users.map(async (user)=>{\n            if (user.departmentId) {\n                try {\n                    const deptDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'departments', user.departmentId));\n                    if (deptDoc.exists()) {\n                        const deptData = deptDoc.data();\n                        return {\n                            ...user,\n                            departmentName: deptData.name || null\n                        };\n                    }\n                } catch (error) {\n                    console.error(`Error fetching department for user ${user.id}:`, error);\n                }\n            }\n            return user;\n        }));\n        return usersWithDepartments;\n    } catch (error) {\n        console.error('Error getting users:', error);\n        throw error;\n    }\n}\n// Get user by ID\nasync function getUserById(id) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id);\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!snapshot.exists()) {\n            return null;\n        }\n        return userConverter.fromFirestore(snapshot);\n    } catch (error) {\n        console.error(`Error getting user ${id}:`, error);\n        throw error;\n    }\n}\n// Get user by email\nasync function getUserByEmail(email) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('email', '==', email));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (snapshot.empty) {\n            return null;\n        }\n        return userConverter.fromFirestore(snapshot.docs[0]);\n    } catch (error) {\n        console.error(`Error getting user by email ${email}:`, error);\n        throw error;\n    }\n}\n// Create new user\nasync function createUser(userData) {\n    try {\n        const data = {\n            ...userData,\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)(),\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        const docRef = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.addDoc)(usersCollection, data);\n        const newDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        return userConverter.fromFirestore(newDoc);\n    } catch (error) {\n        console.error('Error creating user:', error);\n        throw error;\n    }\n}\n// Update user\nasync function updateUser(id, userData) {\n    try {\n        const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id);\n        // Add updatedAt timestamp\n        const data = {\n            ...userData,\n            updatedAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.updateDoc)(docRef, data);\n        // Get updated document\n        const updatedDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(docRef);\n        if (!updatedDoc.exists()) {\n            throw new Error(`User with ID ${id} not found`);\n        }\n        return userConverter.fromFirestore(updatedDoc);\n    } catch (error) {\n        console.error(`Error updating user ${id}:`, error);\n        throw error;\n    }\n}\n// Delete user\nasync function deleteUser(id) {\n    try {\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_0__.db, 'users', id));\n    } catch (error) {\n        console.error(`Error deleting user ${id}:`, error);\n        throw error;\n    }\n}\nasync function getUsersByDepartment(departmentId) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('departmentId', '==', departmentId));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting users by department ${departmentId}:`, error);\n        throw error;\n    }\n}\nasync function getUsersByRole(role) {\n    try {\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersCollection, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('role', '==', role));\n        const snapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        return snapshot.docs.map((doc)=>userConverter.fromFirestore(doc));\n    } catch (error) {\n        console.error(`Error getting users by role ${role}:`, error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc2VydmljZXMvdXNlclNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFvQztBQWNSO0FBRzVCLG1DQUFtQztBQUNuQyxTQUFTVyxTQUFTQyxJQUFTO0lBQ3pCLElBQUksQ0FBQ0EsTUFBTSxPQUFPO0lBRWxCLElBQUk7UUFDRix3QkFBd0I7UUFDeEIsSUFBSUEsZ0JBQWdCQyxNQUFNO1lBQ3hCLE9BQU9EO1FBQ1Q7UUFFQSwwREFBMEQ7UUFDMUQsSUFBSSxPQUFPQSxTQUFTLFVBQVU7WUFDNUIsbUJBQW1CO1lBQ25CLElBQUlBLEtBQUtFLEtBQUssQ0FBQyx5QkFBeUJGLEtBQUtHLFFBQVEsQ0FBQyxjQUFjO2dCQUNsRSxNQUFNQyxhQUFhLElBQUlILEtBQUtEO2dCQUM1QixJQUFJLENBQUNLLE1BQU1ELFdBQVdFLE9BQU8sS0FBSztvQkFDaEMsT0FBT0Y7Z0JBQ1Q7WUFDRjtZQUVBLGdDQUFnQztZQUNoQyxNQUFNQSxhQUFhLElBQUlILEtBQUtEO1lBQzVCLElBQUksQ0FBQ0ssTUFBTUQsV0FBV0UsT0FBTyxLQUFLO2dCQUNoQyxPQUFPRjtZQUNUO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsSUFBSSxPQUFPSixTQUFTLFVBQVU7WUFDNUIsT0FBTyxJQUFJQyxLQUFLRDtRQUNsQjtRQUVBLDBDQUEwQztRQUMxQyxJQUFJQSxRQUFRLE9BQU9BLFNBQVMsWUFBWSxZQUFZQSxRQUFRLE9BQU9BLEtBQUtPLE1BQU0sS0FBSyxZQUFZO1lBQzdGLE9BQU9QLEtBQUtPLE1BQU07UUFDcEI7UUFFQSw4REFBOEQ7UUFDOUQsSUFBSVAsUUFBUSxPQUFPQSxTQUFTLFlBQVksYUFBYUEsTUFBTTtZQUN6RCxPQUFPLElBQUlDLEtBQUtELEtBQUtRLE9BQU8sR0FBRztRQUNqQztRQUVBLG9EQUFvRDtRQUNwRCxJQUFJLE9BQU9SLFNBQVMsWUFBYUEsQ0FBQUEsS0FBS0csUUFBUSxDQUFDLGdCQUFnQkgsS0FBS0csUUFBUSxDQUFDLGdCQUFlLEdBQUk7WUFDOUYsSUFBSTtnQkFDRixNQUFNTSxTQUFTQyxLQUFLQyxLQUFLLENBQUNYO2dCQUMxQixJQUFJUyxVQUFVLE9BQU9BLFdBQVcsWUFBWSxhQUFhQSxRQUFRO29CQUMvRCxPQUFPLElBQUlSLEtBQUtRLE9BQU9ELE9BQU8sR0FBRztnQkFDbkM7WUFDRixFQUFFLE9BQU9JLEdBQUcsQ0FBQztRQUNmO0lBQ0YsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO0lBQ2hEO0lBRUEsT0FBTztBQUNUO0FBZ0JBLDRDQUE0QztBQUM1QyxNQUFNRSxnQkFBZ0I7SUFDcEJDLGVBQWNDLFFBQStCO1FBQzNDLE1BQU1DLE9BQU9ELFNBQVNDLElBQUk7UUFFMUIsOEJBQThCO1FBQzlCLElBQUlDLFlBQVksSUFBSWxCO1FBQ3BCLElBQUltQixZQUFZLElBQUluQjtRQUVwQixJQUFJaUIsS0FBS0MsU0FBUyxFQUFFO1lBQ2xCQSxZQUFZcEIsU0FBU21CLEtBQUtDLFNBQVMsS0FBSyxJQUFJbEI7UUFDOUM7UUFFQSxJQUFJaUIsS0FBS0UsU0FBUyxFQUFFO1lBQ2xCQSxZQUFZckIsU0FBU21CLEtBQUtFLFNBQVMsS0FBSyxJQUFJbkI7UUFDOUM7UUFFQSxPQUFPO1lBQ0xvQixJQUFJSixTQUFTSSxFQUFFO1lBQ2ZDLE1BQU1KLEtBQUtJLElBQUksSUFBSTtZQUNuQkMsT0FBT0wsS0FBS0ssS0FBSyxJQUFJO1lBQ3JCQyxVQUFVTixLQUFLTSxRQUFRLElBQUlDO1lBQzNCQyxNQUFNUixLQUFLUSxJQUFJLElBQUk7WUFDbkJDLFlBQVlULEtBQUtTLFVBQVUsSUFBSTtZQUMvQkMsY0FBY1YsS0FBS1UsWUFBWSxJQUFJO1lBQ25DVCxXQUFXQTtZQUNYQyxXQUFXQTtRQUNiO0lBQ0Y7SUFDQVMsYUFBWUMsSUFBbUI7UUFDN0IsTUFBTSxFQUFFTixRQUFRLEVBQUUsR0FBR08sVUFBVSxHQUFHRDtRQUNsQyxPQUFPO1lBQ0wsR0FBR0MsUUFBUTtZQUNYWCxXQUFXdEIsbUVBQWVBO1FBQzVCO0lBQ0Y7QUFDRjtBQUVBLHVCQUF1QjtBQUN2QixNQUFNa0Msa0JBQWtCM0MsOERBQVVBLENBQUNELDZDQUFFQSxFQUFFO0FBRXZDLGdCQUFnQjtBQUNULGVBQWU2QztJQUNwQixJQUFJO1FBQ0YsTUFBTWhCLFdBQVcsTUFBTTFCLDJEQUFPQSxDQUFDeUM7UUFDL0IsTUFBTUUsUUFBUWpCLFNBQVNrQixJQUFJLENBQUNDLEdBQUcsQ0FBQzlDLENBQUFBLE1BQU95QixjQUFjQyxhQUFhLENBQUMxQjtRQUVuRSx1Q0FBdUM7UUFDdkMsTUFBTStDLHVCQUF1QixNQUFNQyxRQUFRQyxHQUFHLENBQUNMLE1BQU1FLEdBQUcsQ0FBQyxPQUFPTjtZQUM5RCxJQUFJQSxLQUFLRixZQUFZLEVBQUU7Z0JBQ3JCLElBQUk7b0JBQ0YsTUFBTVksVUFBVSxNQUFNaEQsMERBQU1BLENBQUNGLHVEQUFHQSxDQUFDRiw2Q0FBRUEsRUFBRSxlQUFlMEMsS0FBS0YsWUFBWTtvQkFDckUsSUFBSVksUUFBUUMsTUFBTSxJQUFJO3dCQUNwQixNQUFNQyxXQUFXRixRQUFRdEIsSUFBSTt3QkFDN0IsT0FBTzs0QkFDTCxHQUFHWSxJQUFJOzRCQUNQYSxnQkFBZ0JELFNBQVNwQixJQUFJLElBQUk7d0JBQ25DO29CQUNGO2dCQUNGLEVBQUUsT0FBT1QsT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLENBQUMsbUNBQW1DLEVBQUVpQixLQUFLVCxFQUFFLENBQUMsQ0FBQyxDQUFDLEVBQUVSO2dCQUNsRTtZQUNGO1lBQ0EsT0FBT2lCO1FBQ1Q7UUFFQSxPQUFPTztJQUNULEVBQUUsT0FBT3hCLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7UUFDdEMsTUFBTUE7SUFDUjtBQUNGO0FBRUEsaUJBQWlCO0FBQ1YsZUFBZStCLFlBQVl2QixFQUFVO0lBQzFDLElBQUk7UUFDRixNQUFNd0IsU0FBU3ZELHVEQUFHQSxDQUFDRiw2Q0FBRUEsRUFBRSxTQUFTaUM7UUFDaEMsTUFBTUosV0FBVyxNQUFNekIsMERBQU1BLENBQUNxRDtRQUU5QixJQUFJLENBQUM1QixTQUFTd0IsTUFBTSxJQUFJO1lBQ3RCLE9BQU87UUFDVDtRQUVBLE9BQU8xQixjQUFjQyxhQUFhLENBQUNDO0lBQ3JDLEVBQUUsT0FBT0osT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxtQkFBbUIsRUFBRVEsR0FBRyxDQUFDLENBQUMsRUFBRVI7UUFDM0MsTUFBTUE7SUFDUjtBQUNGO0FBRUEsb0JBQW9CO0FBQ2IsZUFBZWlDLGVBQWV2QixLQUFhO0lBQ2hELElBQUk7UUFDRixNQUFNd0IsSUFBSW5ELHlEQUFLQSxDQUFDb0MsaUJBQWlCbkMseURBQUtBLENBQUMsU0FBUyxNQUFNMEI7UUFDdEQsTUFBTU4sV0FBVyxNQUFNMUIsMkRBQU9BLENBQUN3RDtRQUUvQixJQUFJOUIsU0FBUytCLEtBQUssRUFBRTtZQUNsQixPQUFPO1FBQ1Q7UUFFQSxPQUFPakMsY0FBY0MsYUFBYSxDQUFDQyxTQUFTa0IsSUFBSSxDQUFDLEVBQUU7SUFDckQsRUFBRSxPQUFPdEIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyw0QkFBNEIsRUFBRVUsTUFBTSxDQUFDLENBQUMsRUFBRVY7UUFDdkQsTUFBTUE7SUFDUjtBQUNGO0FBRUEsa0JBQWtCO0FBQ1gsZUFBZW9DLFdBQVdsQixRQUFzRDtJQUNyRixJQUFJO1FBQ0YsTUFBTWIsT0FBTztZQUNYLEdBQUdhLFFBQVE7WUFDWFosV0FBV3JCLG1FQUFlQTtZQUMxQnNCLFdBQVd0QixtRUFBZUE7UUFDNUI7UUFFQSxNQUFNK0MsU0FBUyxNQUFNcEQsMERBQU1BLENBQUN1QyxpQkFBaUJkO1FBQzdDLE1BQU1nQyxTQUFTLE1BQU0xRCwwREFBTUEsQ0FBQ3FEO1FBRTVCLE9BQU85QixjQUFjQyxhQUFhLENBQUNrQztJQUNyQyxFQUFFLE9BQU9yQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBLGNBQWM7QUFDUCxlQUFlc0MsV0FBVzlCLEVBQVUsRUFBRVUsUUFBdUI7SUFDbEUsSUFBSTtRQUNGLE1BQU1jLFNBQVN2RCx1REFBR0EsQ0FBQ0YsNkNBQUVBLEVBQUUsU0FBU2lDO1FBRWhDLDBCQUEwQjtRQUMxQixNQUFNSCxPQUFPO1lBQ1gsR0FBR2EsUUFBUTtZQUNYWCxXQUFXdEIsbUVBQWVBO1FBQzVCO1FBRUEsTUFBTUosNkRBQVNBLENBQUNtRCxRQUFRM0I7UUFFeEIsdUJBQXVCO1FBQ3ZCLE1BQU1rQyxhQUFhLE1BQU01RCwwREFBTUEsQ0FBQ3FEO1FBRWhDLElBQUksQ0FBQ08sV0FBV1gsTUFBTSxJQUFJO1lBQ3hCLE1BQU0sSUFBSVksTUFBTSxDQUFDLGFBQWEsRUFBRWhDLEdBQUcsVUFBVSxDQUFDO1FBQ2hEO1FBRUEsT0FBT04sY0FBY0MsYUFBYSxDQUFDb0M7SUFDckMsRUFBRSxPQUFPdkMsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsRUFBRVEsR0FBRyxDQUFDLENBQUMsRUFBRVI7UUFDNUMsTUFBTUE7SUFDUjtBQUNGO0FBRUEsY0FBYztBQUNQLGVBQWV5QyxXQUFXakMsRUFBVTtJQUN6QyxJQUFJO1FBQ0YsTUFBTTFCLDZEQUFTQSxDQUFDTCx1REFBR0EsQ0FBQ0YsNkNBQUVBLEVBQUUsU0FBU2lDO0lBQ25DLEVBQUUsT0FBT1IsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsRUFBRVEsR0FBRyxDQUFDLENBQUMsRUFBRVI7UUFDNUMsTUFBTUE7SUFDUjtBQUNGO0FBRU8sZUFBZTBDLHFCQUFxQjNCLFlBQW9CO0lBQzdELElBQUk7UUFDRixNQUFNbUIsSUFBSW5ELHlEQUFLQSxDQUFDb0MsaUJBQWlCbkMseURBQUtBLENBQUMsZ0JBQWdCLE1BQU0rQjtRQUM3RCxNQUFNWCxXQUFXLE1BQU0xQiwyREFBT0EsQ0FBQ3dEO1FBQy9CLE9BQU85QixTQUFTa0IsSUFBSSxDQUFDQyxHQUFHLENBQUM5QyxDQUFBQSxNQUFPeUIsY0FBY0MsYUFBYSxDQUFDMUI7SUFDOUQsRUFBRSxPQUFPdUIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxrQ0FBa0MsRUFBRWUsYUFBYSxDQUFDLENBQUMsRUFBRWY7UUFDcEUsTUFBTUE7SUFDUjtBQUNGO0FBRU8sZUFBZTJDLGVBQWU5QixJQUE4QjtJQUNqRSxJQUFJO1FBQ0YsTUFBTXFCLElBQUluRCx5REFBS0EsQ0FBQ29DLGlCQUFpQm5DLHlEQUFLQSxDQUFDLFFBQVEsTUFBTTZCO1FBQ3JELE1BQU1ULFdBQVcsTUFBTTFCLDJEQUFPQSxDQUFDd0Q7UUFDL0IsT0FBTzlCLFNBQVNrQixJQUFJLENBQUNDLEdBQUcsQ0FBQzlDLENBQUFBLE1BQU95QixjQUFjQyxhQUFhLENBQUMxQjtJQUM5RCxFQUFFLE9BQU91QixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxDQUFDLDRCQUE0QixFQUFFYSxLQUFLLENBQUMsQ0FBQyxFQUFFYjtRQUN0RCxNQUFNQTtJQUNSO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbW9oZDlcXERvd25sb2Fkc1xccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxccHJvamVjdC1tYW5hZ2VtZW50LWFwcFxcc3JjXFxzZXJ2aWNlc1xcdXNlclNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGIgfSBmcm9tICdAL2xpYi9maXJlYmFzZSc7XHJcbmltcG9ydCB7IFxyXG4gIGNvbGxlY3Rpb24sIFxyXG4gIGRvYywgXHJcbiAgZ2V0RG9jcywgXHJcbiAgZ2V0RG9jLCBcclxuICBhZGREb2MsIFxyXG4gIHVwZGF0ZURvYywgXHJcbiAgZGVsZXRlRG9jLCBcclxuICBxdWVyeSwgXHJcbiAgd2hlcmUsIFxyXG4gIHNlcnZlclRpbWVzdGFtcCxcclxuICBEb2N1bWVudERhdGEsXHJcbiAgUXVlcnlEb2N1bWVudFNuYXBzaG90XHJcbn0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcclxuaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMvZGF0ZVV0aWxzJztcclxuXHJcbi8vIExvY2FsIGltcGxlbWVudGF0aW9uIG9mIHRvSnNEYXRlXHJcbmZ1bmN0aW9uIHRvSnNEYXRlKGRhdGU6IGFueSk6IERhdGUgfCBudWxsIHtcclxuICBpZiAoIWRhdGUpIHJldHVybiBudWxsO1xyXG4gIFxyXG4gIHRyeSB7XHJcbiAgICAvLyBBbHJlYWR5IGEgRGF0ZSBvYmplY3RcclxuICAgIGlmIChkYXRlIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICByZXR1cm4gZGF0ZTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gU3RyaW5nIGRhdGUgKElTTyBmb3JtYXQgb3Igb3RoZXIgc3RyaW5nIHJlcHJlc2VudGF0aW9uKVxyXG4gICAgaWYgKHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJykge1xyXG4gICAgICAvLyBIYW5kbGUgSVNPIGRhdGVzXHJcbiAgICAgIGlmIChkYXRlLm1hdGNoKC9eXFxkezR9LVxcZHsyfS1cXGR7Mn0vKSB8fCBkYXRlLmluY2x1ZGVzKCdUMDA6MDA6MDAnKSkge1xyXG4gICAgICAgIGNvbnN0IHBhcnNlZERhdGUgPSBuZXcgRGF0ZShkYXRlKTtcclxuICAgICAgICBpZiAoIWlzTmFOKHBhcnNlZERhdGUuZ2V0VGltZSgpKSkge1xyXG4gICAgICAgICAgcmV0dXJuIHBhcnNlZERhdGU7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBUcnkgdG8gcGFyc2UgYXMgYSBkYXRlIGFueXdheVxyXG4gICAgICBjb25zdCBwYXJzZWREYXRlID0gbmV3IERhdGUoZGF0ZSk7XHJcbiAgICAgIGlmICghaXNOYU4ocGFyc2VkRGF0ZS5nZXRUaW1lKCkpKSB7XHJcbiAgICAgICAgcmV0dXJuIHBhcnNlZERhdGU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gTnVtZXJpYyB0aW1lc3RhbXAgKG1pbGxpc2Vjb25kcyBzaW5jZSBlcG9jaClcclxuICAgIGlmICh0eXBlb2YgZGF0ZSA9PT0gJ251bWJlcicpIHtcclxuICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGUpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBGaXJlYmFzZSBUaW1lc3RhbXAgd2l0aCB0b0RhdGUoKSBtZXRob2RcclxuICAgIGlmIChkYXRlICYmIHR5cGVvZiBkYXRlID09PSAnb2JqZWN0JyAmJiAndG9EYXRlJyBpbiBkYXRlICYmIHR5cGVvZiBkYXRlLnRvRGF0ZSA9PT0gJ2Z1bmN0aW9uJykge1xyXG4gICAgICByZXR1cm4gZGF0ZS50b0RhdGUoKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gRmlyZWJhc2UgVGltZXN0YW1wLWxpa2Ugb2JqZWN0IHdpdGggc2Vjb25kcyBhbmQgbmFub3NlY29uZHNcclxuICAgIGlmIChkYXRlICYmIHR5cGVvZiBkYXRlID09PSAnb2JqZWN0JyAmJiAnc2Vjb25kcycgaW4gZGF0ZSkge1xyXG4gICAgICByZXR1cm4gbmV3IERhdGUoZGF0ZS5zZWNvbmRzICogMTAwMCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIFN0cmluZ2lmaWVkIG9iamVjdCB0aGF0IG1pZ2h0IGNvbnRhaW4gYSB0aW1lc3RhbXBcclxuICAgIGlmICh0eXBlb2YgZGF0ZSA9PT0gJ3N0cmluZycgJiYgKGRhdGUuaW5jbHVkZXMoJ1wic2Vjb25kc1wiJykgfHwgZGF0ZS5pbmNsdWRlcygnXCJuYW5vc2Vjb25kc1wiJykpKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShkYXRlKTtcclxuICAgICAgICBpZiAocGFyc2VkICYmIHR5cGVvZiBwYXJzZWQgPT09ICdvYmplY3QnICYmICdzZWNvbmRzJyBpbiBwYXJzZWQpIHtcclxuICAgICAgICAgIHJldHVybiBuZXcgRGF0ZShwYXJzZWQuc2Vjb25kcyAqIDEwMDApO1xyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZSkge31cclxuICAgIH1cclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY29udmVydGluZyB0byBKUyBEYXRlOicsIGVycm9yKTtcclxuICB9XHJcbiAgXHJcbiAgcmV0dXJuIG51bGw7XHJcbn1cclxuXHJcbi8vIFVzZXIgdHlwZSBkZWZpbml0aW9uXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBuYW1lOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBwYXNzd29yZD86IHN0cmluZztcclxuICByb2xlOiBzdHJpbmc7XHJcbiAgZGVwYXJ0bWVudDogc3RyaW5nIHwgbnVsbDtcclxuICBkZXBhcnRtZW50SWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG4gIHVwZGF0ZWRBdDogRGF0ZTtcclxuICBkZXBhcnRtZW50TmFtZT86IHN0cmluZztcclxufVxyXG5cclxuLy8gQ29udmVydCBGaXJlc3RvcmUgZG9jdW1lbnQgdG8gVXNlciBvYmplY3RcclxuY29uc3QgdXNlckNvbnZlcnRlciA9IHtcclxuICBmcm9tRmlyZXN0b3JlKHNuYXBzaG90OiBRdWVyeURvY3VtZW50U25hcHNob3QpOiBVc2VyIHtcclxuICAgIGNvbnN0IGRhdGEgPSBzbmFwc2hvdC5kYXRhKCk7XHJcbiAgICBcclxuICAgIC8vIEhhbmRsZSBGaXJlc3RvcmUgdGltZXN0YW1wc1xyXG4gICAgbGV0IGNyZWF0ZWRBdCA9IG5ldyBEYXRlKCk7XHJcbiAgICBsZXQgdXBkYXRlZEF0ID0gbmV3IERhdGUoKTtcclxuICAgIFxyXG4gICAgaWYgKGRhdGEuY3JlYXRlZEF0KSB7XHJcbiAgICAgIGNyZWF0ZWRBdCA9IHRvSnNEYXRlKGRhdGEuY3JlYXRlZEF0KSB8fCBuZXcgRGF0ZSgpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoZGF0YS51cGRhdGVkQXQpIHtcclxuICAgICAgdXBkYXRlZEF0ID0gdG9Kc0RhdGUoZGF0YS51cGRhdGVkQXQpIHx8IG5ldyBEYXRlKCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlkOiBzbmFwc2hvdC5pZCxcclxuICAgICAgbmFtZTogZGF0YS5uYW1lIHx8ICcnLFxyXG4gICAgICBlbWFpbDogZGF0YS5lbWFpbCB8fCAnJyxcclxuICAgICAgcGFzc3dvcmQ6IGRhdGEucGFzc3dvcmQgfHwgdW5kZWZpbmVkLFxyXG4gICAgICByb2xlOiBkYXRhLnJvbGUgfHwgJ1VTRVInLFxyXG4gICAgICBkZXBhcnRtZW50OiBkYXRhLmRlcGFydG1lbnQgfHwgbnVsbCxcclxuICAgICAgZGVwYXJ0bWVudElkOiBkYXRhLmRlcGFydG1lbnRJZCB8fCBudWxsLFxyXG4gICAgICBjcmVhdGVkQXQ6IGNyZWF0ZWRBdCxcclxuICAgICAgdXBkYXRlZEF0OiB1cGRhdGVkQXRcclxuICAgIH07XHJcbiAgfSxcclxuICB0b0ZpcmVzdG9yZSh1c2VyOiBQYXJ0aWFsPFVzZXI+KTogRG9jdW1lbnREYXRhIHtcclxuICAgIGNvbnN0IHsgcGFzc3dvcmQsIC4uLnVzZXJEYXRhIH0gPSB1c2VyO1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLi4udXNlckRhdGEsXHJcbiAgICAgIHVwZGF0ZWRBdDogc2VydmVyVGltZXN0YW1wKClcclxuICAgIH07XHJcbiAgfVxyXG59O1xyXG5cclxuLy8gQ29sbGVjdGlvbiByZWZlcmVuY2VcclxuY29uc3QgdXNlcnNDb2xsZWN0aW9uID0gY29sbGVjdGlvbihkYiwgJ3VzZXJzJyk7XHJcblxyXG4vLyBHZXQgYWxsIHVzZXJzXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBbGxVc2VycygpOiBQcm9taXNlPFVzZXJbXT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBzbmFwc2hvdCA9IGF3YWl0IGdldERvY3ModXNlcnNDb2xsZWN0aW9uKTtcclxuICAgIGNvbnN0IHVzZXJzID0gc25hcHNob3QuZG9jcy5tYXAoZG9jID0+IHVzZXJDb252ZXJ0ZXIuZnJvbUZpcmVzdG9yZShkb2MpKTtcclxuICAgIFxyXG4gICAgLy8gRmV0Y2ggZGVwYXJ0bWVudCBuYW1lcyBmb3IgZWFjaCB1c2VyXHJcbiAgICBjb25zdCB1c2Vyc1dpdGhEZXBhcnRtZW50cyA9IGF3YWl0IFByb21pc2UuYWxsKHVzZXJzLm1hcChhc3luYyAodXNlcikgPT4ge1xyXG4gICAgICBpZiAodXNlci5kZXBhcnRtZW50SWQpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgZGVwdERvYyA9IGF3YWl0IGdldERvYyhkb2MoZGIsICdkZXBhcnRtZW50cycsIHVzZXIuZGVwYXJ0bWVudElkKSk7XHJcbiAgICAgICAgICBpZiAoZGVwdERvYy5leGlzdHMoKSkge1xyXG4gICAgICAgICAgICBjb25zdCBkZXB0RGF0YSA9IGRlcHREb2MuZGF0YSgpO1xyXG4gICAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICAgIC4uLnVzZXIsXHJcbiAgICAgICAgICAgICAgZGVwYXJ0bWVudE5hbWU6IGRlcHREYXRhLm5hbWUgfHwgbnVsbFxyXG4gICAgICAgICAgICB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBmZXRjaGluZyBkZXBhcnRtZW50IGZvciB1c2VyICR7dXNlci5pZH06YCwgZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gdXNlcjtcclxuICAgIH0pKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHVzZXJzV2l0aERlcGFydG1lbnRzO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXJzOicsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLy8gR2V0IHVzZXIgYnkgSURcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPFVzZXIgfCBudWxsPiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGRvY1JlZiA9IGRvYyhkYiwgJ3VzZXJzJywgaWQpO1xyXG4gICAgY29uc3Qgc25hcHNob3QgPSBhd2FpdCBnZXREb2MoZG9jUmVmKTtcclxuICAgIFxyXG4gICAgaWYgKCFzbmFwc2hvdC5leGlzdHMoKSkge1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHVzZXJDb252ZXJ0ZXIuZnJvbUZpcmVzdG9yZShzbmFwc2hvdCBhcyBRdWVyeURvY3VtZW50U25hcHNob3QpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciBnZXR0aW5nIHVzZXIgJHtpZH06YCwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBHZXQgdXNlciBieSBlbWFpbFxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlckJ5RW1haWwoZW1haWw6IHN0cmluZyk6IFByb21pc2U8VXNlciB8IG51bGw+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcSA9IHF1ZXJ5KHVzZXJzQ29sbGVjdGlvbiwgd2hlcmUoJ2VtYWlsJywgJz09JywgZW1haWwpKTtcclxuICAgIGNvbnN0IHNuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhxKTtcclxuICAgIFxyXG4gICAgaWYgKHNuYXBzaG90LmVtcHR5KSB7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICByZXR1cm4gdXNlckNvbnZlcnRlci5mcm9tRmlyZXN0b3JlKHNuYXBzaG90LmRvY3NbMF0gYXMgUXVlcnlEb2N1bWVudFNuYXBzaG90KTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgZ2V0dGluZyB1c2VyIGJ5IGVtYWlsICR7ZW1haWx9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLy8gQ3JlYXRlIG5ldyB1c2VyXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVVc2VyKHVzZXJEYXRhOiBPbWl0PFVzZXIsICdpZCcgfCAnY3JlYXRlZEF0JyB8ICd1cGRhdGVkQXQnPik6IFByb21pc2U8VXNlcj4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBkYXRhID0ge1xyXG4gICAgICAuLi51c2VyRGF0YSxcclxuICAgICAgY3JlYXRlZEF0OiBzZXJ2ZXJUaW1lc3RhbXAoKSxcclxuICAgICAgdXBkYXRlZEF0OiBzZXJ2ZXJUaW1lc3RhbXAoKVxyXG4gICAgfTtcclxuICAgIFxyXG4gICAgY29uc3QgZG9jUmVmID0gYXdhaXQgYWRkRG9jKHVzZXJzQ29sbGVjdGlvbiwgZGF0YSk7XHJcbiAgICBjb25zdCBuZXdEb2MgPSBhd2FpdCBnZXREb2MoZG9jUmVmKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIHVzZXJDb252ZXJ0ZXIuZnJvbUZpcmVzdG9yZShuZXdEb2MgYXMgUXVlcnlEb2N1bWVudFNuYXBzaG90KTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgdXNlcjonLCBlcnJvcik7XHJcbiAgICB0aHJvdyBlcnJvcjtcclxuICB9XHJcbn1cclxuXHJcbi8vIFVwZGF0ZSB1c2VyXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVVc2VyKGlkOiBzdHJpbmcsIHVzZXJEYXRhOiBQYXJ0aWFsPFVzZXI+KTogUHJvbWlzZTxVc2VyPiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IGRvY1JlZiA9IGRvYyhkYiwgJ3VzZXJzJywgaWQpO1xyXG4gICAgXHJcbiAgICAvLyBBZGQgdXBkYXRlZEF0IHRpbWVzdGFtcFxyXG4gICAgY29uc3QgZGF0YSA9IHtcclxuICAgICAgLi4udXNlckRhdGEsXHJcbiAgICAgIHVwZGF0ZWRBdDogc2VydmVyVGltZXN0YW1wKClcclxuICAgIH07XHJcbiAgICBcclxuICAgIGF3YWl0IHVwZGF0ZURvYyhkb2NSZWYsIGRhdGEpO1xyXG4gICAgXHJcbiAgICAvLyBHZXQgdXBkYXRlZCBkb2N1bWVudFxyXG4gICAgY29uc3QgdXBkYXRlZERvYyA9IGF3YWl0IGdldERvYyhkb2NSZWYpO1xyXG4gICAgXHJcbiAgICBpZiAoIXVwZGF0ZWREb2MuZXhpc3RzKCkpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBVc2VyIHdpdGggSUQgJHtpZH0gbm90IGZvdW5kYCk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiB1c2VyQ29udmVydGVyLmZyb21GaXJlc3RvcmUodXBkYXRlZERvYyBhcyBRdWVyeURvY3VtZW50U25hcHNob3QpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciB1cGRhdGluZyB1c2VyICR7aWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuLy8gRGVsZXRlIHVzZXJcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRlbGV0ZVVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xyXG4gIHRyeSB7XHJcbiAgICBhd2FpdCBkZWxldGVEb2MoZG9jKGRiLCAndXNlcnMnLCBpZCkpO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciBkZWxldGluZyB1c2VyICR7aWR9OmAsIGVycm9yKTtcclxuICAgIHRocm93IGVycm9yO1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFVzZXJzQnlEZXBhcnRtZW50KGRlcGFydG1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyW10+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcSA9IHF1ZXJ5KHVzZXJzQ29sbGVjdGlvbiwgd2hlcmUoJ2RlcGFydG1lbnRJZCcsICc9PScsIGRlcGFydG1lbnRJZCkpO1xyXG4gICAgY29uc3Qgc25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpO1xyXG4gICAgcmV0dXJuIHNuYXBzaG90LmRvY3MubWFwKGRvYyA9PiB1c2VyQ29udmVydGVyLmZyb21GaXJlc3RvcmUoZG9jKSk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGdldHRpbmcgdXNlcnMgYnkgZGVwYXJ0bWVudCAke2RlcGFydG1lbnRJZH06YCwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlcnNCeVJvbGUocm9sZTogJ1VTRVInIHwgJ0FETUlOJyB8ICdQTU8nKTogUHJvbWlzZTxVc2VyW10+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcSA9IHF1ZXJ5KHVzZXJzQ29sbGVjdGlvbiwgd2hlcmUoJ3JvbGUnLCAnPT0nLCByb2xlKSk7XHJcbiAgICBjb25zdCBzbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XHJcbiAgICByZXR1cm4gc25hcHNob3QuZG9jcy5tYXAoZG9jID0+IHVzZXJDb252ZXJ0ZXIuZnJvbUZpcmVzdG9yZShkb2MpKTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihgRXJyb3IgZ2V0dGluZyB1c2VycyBieSByb2xlICR7cm9sZX06YCwgZXJyb3IpO1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbiAgfVxyXG59Il0sIm5hbWVzIjpbImRiIiwiY29sbGVjdGlvbiIsImRvYyIsImdldERvY3MiLCJnZXREb2MiLCJhZGREb2MiLCJ1cGRhdGVEb2MiLCJkZWxldGVEb2MiLCJxdWVyeSIsIndoZXJlIiwic2VydmVyVGltZXN0YW1wIiwidG9Kc0RhdGUiLCJkYXRlIiwiRGF0ZSIsIm1hdGNoIiwiaW5jbHVkZXMiLCJwYXJzZWREYXRlIiwiaXNOYU4iLCJnZXRUaW1lIiwidG9EYXRlIiwic2Vjb25kcyIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsImUiLCJlcnJvciIsImNvbnNvbGUiLCJ1c2VyQ29udmVydGVyIiwiZnJvbUZpcmVzdG9yZSIsInNuYXBzaG90IiwiZGF0YSIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsImlkIiwibmFtZSIsImVtYWlsIiwicGFzc3dvcmQiLCJ1bmRlZmluZWQiLCJyb2xlIiwiZGVwYXJ0bWVudCIsImRlcGFydG1lbnRJZCIsInRvRmlyZXN0b3JlIiwidXNlciIsInVzZXJEYXRhIiwidXNlcnNDb2xsZWN0aW9uIiwiZ2V0QWxsVXNlcnMiLCJ1c2VycyIsImRvY3MiLCJtYXAiLCJ1c2Vyc1dpdGhEZXBhcnRtZW50cyIsIlByb21pc2UiLCJhbGwiLCJkZXB0RG9jIiwiZXhpc3RzIiwiZGVwdERhdGEiLCJkZXBhcnRtZW50TmFtZSIsImdldFVzZXJCeUlkIiwiZG9jUmVmIiwiZ2V0VXNlckJ5RW1haWwiLCJxIiwiZW1wdHkiLCJjcmVhdGVVc2VyIiwibmV3RG9jIiwidXBkYXRlVXNlciIsInVwZGF0ZWREb2MiLCJFcnJvciIsImRlbGV0ZVVzZXIiLCJnZXRVc2Vyc0J5RGVwYXJ0bWVudCIsImdldFVzZXJzQnlSb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/services/userService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@firebase","vendor-chunks/undici","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fteam%2Froute&page=%2Fapi%2Fteam%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam%2Froute.ts&appDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmohd9%5CDownloads%5Cproject-management-app%5Cproject-management-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();