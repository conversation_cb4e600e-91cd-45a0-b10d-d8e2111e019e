"use client";

import { useState, useEffect } from 'react';
import AppLayout from '@/components/AppLayout';
import AuthWrapper from '@/components/AuthWrapper';
import { EnvelopeIcon, PhoneIcon, MapPinIcon, PlusIcon, PencilIcon, TrashIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useSession } from 'next-auth/react';
import { Session } from 'next-auth';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string | null;
  departmentId?: string | null;
  departmentName?: string | null;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  tasks?: { id: string }[];
  projectCount?: number;
  projects?: Array<{
    id: string;
    title: string;
    status: string;
    percentage: number;
  }>;
}

interface TeamMemberFormData {
  id?: string;
  name: string;
  email: string;
  password?: string;
  role: string;
  department?: string;
  departmentId?: string;
}

interface CustomSession extends Session {
  user: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string;
  }
}

interface Department {
  id: string;
  name: string;
  description?: string;
}

export default function TeamPage() {
  const { data: session } = useSession() as { data: CustomSession | null };
  const router = useRouter();
  const [teamMembers, setTeamMembers] = useState<User[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<TeamMemberFormData>({
    name: '',
    email: '',
    password: '',
    role: 'USER',
    department: '',
    departmentId: undefined
  });
  const [isEditing, setIsEditing] = useState(false);
  const [expandedProjects, setExpandedProjects] = useState<{[key: string]: boolean}>({});

  const isAdmin = session?.user?.role === 'ADMIN';

  useEffect(() => {
    fetchTeamMembers();
    fetchDepartments();
  }, []);

  const fetchTeamMembers = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('/api/team');
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setTeamMembers(data);
    } catch (error) {
      console.error('Failed to fetch team members:', error);
      setError('Failed to load team members. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await fetch('/api/departments');
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setDepartments(data);
    } catch (error) {
      console.error('Failed to fetch departments:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const url = isEditing ? '/api/team' : '/api/team';
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save team member');
      }

      await fetchTeamMembers();
      setIsModalOpen(false);
      resetForm();
    } catch (error: any) {
      console.error('Error saving team member:', error);
      alert(error.message || 'An error occurred while saving the team member');
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this team member?')) return;

    try {
      const response = await fetch(`/api/team?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete team member');
      }

      await fetchTeamMembers();
    } catch (error: any) {
      console.error('Error deleting team member:', error);
      alert(error.message || 'An error occurred while deleting the team member');
    }
  };

  const handleEdit = (member: User) => {
    setFormData({
      id: member.id,
      name: member.name,
      email: member.email,
      role: member.role,
      department: member.department || '',
      departmentId: member.departmentId || undefined
    });
    setIsEditing(true);
    setIsModalOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      password: '',
      role: 'USER',
      department: '',
      departmentId: undefined
    });
    setIsEditing(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  const toggleProjectsList = (memberId: string) => {
    setExpandedProjects(prev => ({
      ...prev,
      [memberId]: !prev[memberId]
    }));
  };

  return (
    <AuthWrapper>
      <AppLayout title="Team">
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Team Members</h2>
              <p className="text-sm text-slate-500 mt-1">Manage your team and view member details</p>
            </div>
            {isAdmin && (
              <button 
                className="btn btn-primary"
                onClick={() => {
                  resetForm();
                  setIsModalOpen(true);
                }}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Member
              </button>
            )}
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <p className="text-lg">Loading team members...</p>
            </div>
          ) : error ? (
            <div className="card p-6 bg-red-50 border border-red-200">
              <p className="text-red-600">{error}</p>
              <button 
                className="btn btn-secondary mt-4" 
                onClick={fetchTeamMembers}
              >
                Try Again
              </button>
            </div>
          ) : (
            <>
              {/* Team Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="card p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-slate-900">{teamMembers.length}</p>
                    <p className="text-sm text-slate-500">Total Members</p>
                  </div>
                </div>
                <div className="card p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-slate-900">
                      {teamMembers.filter(m => m.role === 'ADMIN').length}
                    </p>
                    <p className="text-sm text-slate-500">Admins</p>
                  </div>
                </div>
                <div className="card p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-slate-900">
                      {new Set(teamMembers.map(m => m.department).filter(Boolean)).size}
                    </p>
                    <p className="text-sm text-slate-500">Departments</p>
                  </div>
                </div>
                <div className="card p-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-slate-900">
                      {teamMembers.reduce((sum, m) => sum + (m.projectCount || 0), 0)}
                    </p>
                    <p className="text-sm text-slate-500">Total Projects</p>
                  </div>
                </div>
              </div>

              {/* Team Members Grid */}
              <div className="card">
                <div className="p-6 border-b border-slate-200">
                  <h3 className="text-lg font-semibold text-slate-900">All Members</h3>
                </div>
                {teamMembers.length === 0 ? (
                  <div className="p-6 text-center">
                    <p className="text-slate-500">No team members found.</p>
                    {isAdmin && (
                      <button 
                        className="btn btn-primary mt-4"
                        onClick={() => {
                          resetForm();
                          setIsModalOpen(true);
                        }}
                      >
                        Add First Team Member
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                    {teamMembers.map((member) => (
                      <div key={member.id} className="card-hover p-4 bg-white rounded-xl shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
                        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                          <div className="flex-shrink-0 h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-medium text-lg">
                            {getInitials(member.name)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-lg font-semibold text-slate-900 truncate">{member.name}</h4>
                            <div className="flex flex-wrap gap-2 mt-1">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                member.role === 'ADMIN' 
                                  ? 'bg-purple-100 text-purple-800' 
                                  : member.role === 'PMO' 
                                  ? 'bg-amber-100 text-amber-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {member.role}
                              </span>
                              {member.department && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  {member.departmentName || member.department}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2 my-4 bg-slate-50 p-3 rounded-lg">
                          <div className="flex items-center text-sm text-slate-600">
                            <EnvelopeIcon className="h-4 w-4 mr-2 text-slate-400" />
                            <span className="truncate">{member.email}</span>
                          </div>
                          {member.department && (
                            <div className="flex items-center text-sm text-slate-600">
                              <MapPinIcon className="h-4 w-4 mr-2 text-slate-400" />
                              <span>{member.department}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-slate-200">
                          <div className="bg-blue-50 px-3 py-2 rounded-lg">
                            <p className="text-xs text-slate-500">Projects</p>
                            <p className="text-lg font-medium text-slate-900 text-center">
                              {member.projectCount || 0}
                            </p>
                          </div>
                          
                          {isAdmin && (
                            <div className="flex space-x-2">
                              <button 
                                onClick={() => handleEdit(member)}
                                className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                                title="Edit member"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button 
                                onClick={() => handleDelete(member.id)}
                                className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                                title="Delete member"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          )}
                        </div>
                        
                        {(member.projectCount && member.projectCount > 0) && member.projects && (
                          <div className="mt-4 pt-4 border-t border-slate-200">
                            <div className="flex justify-between items-center mb-2">
                              <p className="text-xs font-medium text-slate-700">Assigned Projects</p>
                              {member.projects.length > 0 && (
                                <button
                                  onClick={() => toggleProjectsList(member.id)}
                                  className="text-xs text-blue-600 flex items-center hover:text-blue-800 transition-colors"
                                >
                                  {expandedProjects[member.id] ? (
                                    <>
                                      <span className="mr-1">Show less</span>
                                      <ChevronUpIcon className="h-3 w-3" />
                                    </>
                                  ) : (
                                    <>
                                      <span className="mr-1">Show all</span>
                                      <ChevronDownIcon className="h-3 w-3" />
                                    </>
                                  )}
                                </button>
                              )}
                            </div>
                            <div className="space-y-2">
                              {member.projects
                                .slice(0, expandedProjects[member.id] ? undefined : 3)
                                .map(project => (
                                  <div key={project.id} className="px-3 py-2 bg-slate-50 rounded-lg">
                                    <div className="flex justify-between items-center">
                                      <span className="text-xs font-medium text-slate-800 truncate">{project.title}</span>
                                      <div className="flex items-center">
                                        <span className={`text-xs ${
                                          project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                                          project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                                          project.status === 'Planning' ? 'bg-amber-100 text-amber-800' :
                                          project.status === 'On Hold' ? 'bg-purple-100 text-purple-800' :
                                          'bg-slate-100 text-slate-800'
                                        } px-1.5 py-0.5 rounded-full mr-2`}>
                                          {project.status}
                                        </span>
                                        <span className="flex items-center justify-center h-5 w-5 bg-blue-500 text-white text-xs font-medium rounded-full">
                                          {project.percentage}%
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              
                              {!expandedProjects[member.id] && member.projects.length > 3 && (
                                <div className="text-right">
                                  <button 
                                    onClick={() => toggleProjectsList(member.id)}
                                    className="text-xs text-blue-600 font-medium hover:underline cursor-pointer flex items-center justify-end w-full"
                                  >
                                    <span>+{member.projects.length - 3} more projects</span>
                                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Add/Edit Member Modal */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl shadow-xl w-full max-w-md transform transition-all">
              <div className="flex items-center justify-between border-b border-slate-100 p-6">
                <h3 className="text-xl font-semibold text-slate-900">
                  {isEditing ? 'Edit Team Member' : 'Add New Team Member'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    resetForm();
                  }}
                  className="text-slate-400 hover:text-slate-500 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="p-6 space-y-5">
                <div className="space-y-5">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Name</label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="pl-10 pr-4 py-2.5 block w-full rounded-lg border border-slate-200 bg-white text-slate-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                        placeholder="Enter full name"
                        required
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <div className="relative">
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="pl-10 pr-4 py-2.5 block w-full rounded-lg border border-slate-200 bg-white text-slate-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                        placeholder="<EMAIL>"
                        required
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  {!isEditing && (
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">Password</label>
                      <div className="relative">
                        <input
                          type="password"
                          value={formData.password}
                          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                          className="pl-10 pr-4 py-2.5 block w-full rounded-lg border border-slate-200 bg-white text-slate-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                          placeholder="••••••••"
                          required={!isEditing}
                        />
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">Role</label>
                      <div className="relative">
                        <select
                          value={formData.role}
                          onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                          className="pl-10 pr-4 py-2.5 block w-full rounded-lg border border-slate-200 bg-white text-slate-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all appearance-none"
                        >
                          <option value="USER">User</option>
                          <option value="ADMIN">Admin</option>
                          <option value="PMO">PMO</option>
                        </select>
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">Department</label>
                      <div className="relative">
                        <select
                          value={formData.departmentId || ''}
                          onChange={(e) => {
                            const deptId = e.target.value;
                            const dept = departments.find(d => d.id === deptId);
                            setFormData({ 
                              ...formData, 
                              departmentId: deptId || undefined,
                              department: dept?.name || ''
                            });
                          }}
                          className="pl-10 pr-4 py-2.5 block w-full rounded-lg border border-slate-200 bg-white text-slate-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all appearance-none"
                        >
                          <option value="">Select Department</option>
                          {departments.map(dept => (
                            <option key={dept.id} value={dept.id}>
                              {dept.name}
                            </option>
                          ))}
                        </select>
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1.581.814L10 13.197l-4.419 2.617A1 1 0 014 15V4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4 border-t border-slate-100 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setIsModalOpen(false);
                      resetForm();
                    }}
                    className="px-4 py-2 rounded-lg text-slate-700 bg-slate-100 hover:bg-slate-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button 
                    type="submit" 
                    className="px-4 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors flex items-center"
                  >
                    {isEditing ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h1a2 2 0 012 2v7a2 2 0 01-2 2H8a2 2 0 01-2-2v-7a2 2 0 012-2h1v5.586l-1.293-1.293z" />
                        </svg>
                        Save Changes
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Add Member
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </AppLayout>
    </AuthWrapper>
  );
} 